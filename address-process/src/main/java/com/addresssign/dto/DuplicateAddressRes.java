package com.addresssign.dto;


import lombok.Data;

import java.util.LinkedHashSet;

@Data
public class DuplicateAddressRes {

    private String address;
    private LinkedHashSet<String> coinSet;
    private Integer count;

    public DuplicateAddressRes(String address, LinkedHashSet<String> coinSet) {
        this.address = address;
        this.coinSet = coinSet;
        this.count = coinSet.size();
    }

    @Override
    public String toString() {
        return address + ":" + coinSet + " " + coinSet.size() + "次";
    }
}