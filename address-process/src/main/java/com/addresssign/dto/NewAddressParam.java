package com.addresssign.dto;


import com.addresssign.constant.NewAddressStatusConstant;
import lombok.Data;

@Data
public class NewAddressParam {

    private String address;
    private String amount;
    private String checkStatus;


    public NewAddressParam(String address, String amount) {
        this.address = address;
        this.amount = amount;
        this.checkStatus = NewAddressStatusConstant.NO_CHECK;
    }

    @Override
    public String toString() {
        return address + "  " + amount + "  " + checkStatus;
    }
}