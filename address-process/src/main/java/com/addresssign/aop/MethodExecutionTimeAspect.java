package com.addresssign.aop;

import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
public class MethodExecutionTimeAspect {


    @Around("execution(public void com.addresssign.process.DuplicateAddressCheck.start(..)) " +
            "|| execution(public void com.addresssign.process.NewAddressCheck.start(..))")
    public void logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        long costTime = endTime - startTime;
        Method method = getTargetMethod(joinPoint);
        String methodDesc = StringUtils.EMPTY;
        if (!ObjectUtils.isEmpty(method)) {
            MethodDesc annotation = method.getAnnotation(MethodDesc.class);
            if (annotation != null) {
                methodDesc = annotation.value();
            }
        }
        log.info("==================={}耗时 {} ms===================", methodDesc, costTime);
    }

    private Method getTargetMethod(ProceedingJoinPoint joinPoint) {
        try {
            String methodName = joinPoint.getSignature().getName();
            Class<?> targetClass = joinPoint.getTarget().getClass();
            Class<?>[] parameterTypes = ((org.aspectj.lang.reflect.MethodSignature) joinPoint.getSignature()).getParameterTypes();
            return targetClass.getMethod(methodName, parameterTypes);
        } catch (NoSuchMethodException e) {
            log.error("Method not found: {}", e.getMessage());
            return null;
        }
    }

}
