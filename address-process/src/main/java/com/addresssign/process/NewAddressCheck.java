package com.addresssign.process;

import com.addresssign.aop.MethodDesc;
import com.addresssign.auth.Auth;
import com.addresssign.constant.CommonConstant;
import com.addresssign.constant.NewAddressStatusConstant;
import com.addresssign.dto.NewAddressParam;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.blate.dto.OkhttpResp;
import com.blate.dto.ProxyInfo;
import com.blate.util.FileUtils;
import com.blate.util.OkhttpUtils;
import com.blate.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class NewAddressCheck {
    public static String COOKIE;
    private static Boolean isHeaderRead = true;
    private static Map<Integer, String> headMap = new HashMap<>();
    private static List<NewAddressParam> newAddressParamList = new ArrayList<>();

    @MethodDesc("新地址检测")
    public void start(String fileName) {
        String cookieFile = CommonConstant.FILE_BASE_PATH + CommonConstant.COOKIE_SOURCE_FILE;
        String newAddressFile = CommonConstant.FILE_BASE_PATH + fileName;
        Auth.checkFileExist(cookieFile, newAddressFile);
        COOKIE = FileUtils.getTxt(cookieFile);

        EasyExcel.read(newAddressFile, new AnalysisEventListener<Map<String, String>>() {

            @Override
            public void invoke(Map<String, String> data, AnalysisContext analysisContext) {
                if (isHeaderRead) {
                    List<String> values = new ArrayList<>(data.values());
                    for (int i = 0; i < values.size(); i++) {
                        headMap.put(i, values.get(i));
                    }
                    isHeaderRead = false;
                } else {
                    NewAddressParam newAddressParam = new NewAddressParam(data.get(0), data.get(1));
                    newAddressParamList.add(newAddressParam);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                // todo 后期改为线程池加速  且改为追加断点写入
                newAddressParamList.forEach(NewAddressCheck::check);
                String file = CommonConstant.FILE_BASE_PATH + CommonConstant.NEW_ADDRESS_CHECK_RES_FILE + TimeUtils.getNowTimeLowLine(1) + ".txt";
                FileUtils.writeTextOver(file, newAddressParamList);
                log.info(CommonConstant.NEW_ADDRESS_CHECK_RES_FILE + " --> " + file);
            }
        }).headRowNumber(0).sheet().doRead();


    }

    private static void check(NewAddressParam newAddressParam) {

        String address = newAddressParam.getAddress();

        OkhttpResp totalRep = reqTotal(COOKIE, address);
        if (totalRep.getCode() != 200) {
            newAddressParam.setCheckStatus(NewAddressStatusConstant.TRANSFER_NUM_CHECK_FAILURE);
            return;
        }
        JSONObject totalRepJsonObject = JSONObject.parseObject(totalRep.getJsonString());
        int total = totalRepJsonObject.getInteger("data");//得到总共的数据
        int pageSize = 100;//分页设置为最大
        int lastPageIndex = (int) Math.ceil((double) total / pageSize);// 计算最后一页的页码


        OkhttpResp reqFirstTransfer = reqFirstTransfer(COOKIE, address, lastPageIndex, pageSize);
        if (reqFirstTransfer.getCode() != 200) {
            newAddressParam.setCheckStatus(NewAddressStatusConstant.FIRST_TRANSFER_CHECK_FAILURE);
            return;
        }
        JSONObject reqFirstTransferjsonObject = JSONObject.parseObject(reqFirstTransfer.getJsonString());
        JSONArray data = reqFirstTransferjsonObject.getJSONArray("data");
        if (ObjectUtils.isEmpty(data)) {
            newAddressParam.setCheckStatus(NewAddressStatusConstant.ZERO_TRANSFER_ADDRESS);
            return;
        }
        JSONObject lastTransferJsonObject = data.getJSONObject(data.size() - 1);
        String flow = lastTransferJsonObject.getString("flow");
        if (!"in".equals(flow)) {
            newAddressParam.setCheckStatus(NewAddressStatusConstant.NO_NEW_ADDRESS);
            return;
        }
        String amount = lastTransferJsonObject.getString("amount");
        String sourceAmount = newAddressParam.getAmount();
        newAddressParam.setCheckStatus(sourceAmount.equals(amount) ? NewAddressStatusConstant.YES_NEW_ADDRESS : NewAddressStatusConstant.NO_NEW_ADDRESS);
    }

    private static OkhttpResp reqFirstTransfer(String cookie, String address, int page, int pageSize) {
        String url = "https://api-v2.solscan.io/v2/account/transfer?address=" + address + "&page=" + page + "&page_size=" + pageSize + "&remove_spam=false&exclude_amount_zero=false";
        return get(url, cookie, address);
    }

    private static OkhttpResp reqTotal(String cookie, String address) {
        String url = "https://api-v2.solscan.io/v2/account/transfer/total?address=" + address + "&remove_spam=false&exclude_amount_zero=false";
        return get(url, cookie, address);
    }

    private static OkhttpResp get(String url, String cookie, String address) {
        Map<String, String> header = new HashMap<>();
        header.put("Cookie", cookie);
        header.put("origin", "https://solscan.io");
        header.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        return OkhttpUtils.get(url, header, new ProxyInfo("127.0.0.1", 7890));
    }


}