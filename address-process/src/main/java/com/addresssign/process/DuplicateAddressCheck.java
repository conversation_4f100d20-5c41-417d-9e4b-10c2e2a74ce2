package com.addresssign.process;

import com.addresssign.aop.MethodDesc;
import com.addresssign.auth.Auth;
import com.addresssign.constant.CommonConstant;
import com.addresssign.dto.DuplicateAddressRes;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.blate.util.FileUtils;
import com.blate.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;


@Slf4j
@Component
public class DuplicateAddressCheck {
    private static Boolean isHeaderRead = true;
    private static Map<Integer, String> headMap = new HashMap<>();//<excel下标,列标题(币种)>
    private static Map<Integer, List<String>> columnMap = new HashMap<>();//<excel下标,这一列地址>

    private static Map<String, LinkedHashSet<String>> AdddressBiSet = new HashMap<>();//一个地址多次出现过的币种

    @MethodDesc("重复地址检测")
    public void start(String fileName) {
        String duplicateAddressFile = CommonConstant.FILE_BASE_PATH + fileName;
        Auth.checkFileExist(duplicateAddressFile);

        EasyExcel.read(duplicateAddressFile, new AnalysisEventListener<Map<String, String>>() {
            @Override
            public void invoke(Map<String, String> data, AnalysisContext context) {
                if (isHeaderRead) {
                    List<String> values = new ArrayList<>(data.values());
                    for (int i = 0; i < values.size(); i++) {
                        headMap.put(i, values.get(i));
                    }
                    isHeaderRead = false;
                } else {
                    List<String> rowData = new ArrayList<>(data.values());
                    for (int i = 0; i < rowData.size(); i++) {
                        List<String> list = columnMap.computeIfAbsent(i, k -> new ArrayList<>());
                        String row = rowData.get(i);
                        if (!ObjectUtils.isEmpty(row)) {
                            list.add(row);
                        }
                    }
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("===================excel数据预览===================");
                for (int i = 0; i < columnMap.size(); i++) {
                    String desc = headMap.get(i);
                    List<String> blist = columnMap.get(i);
                    log.info("{}: {}", desc, blist);
                    blist.forEach(b -> {
                        AdddressBiSet.computeIfAbsent(b, k -> new LinkedHashSet<>()).add(desc);
                    });
                }
                log.info("===================结果计算中===================");
                List<DuplicateAddressRes> duplicateAddressResList = new ArrayList<>();
                AdddressBiSet.forEach((address, coinSet) -> {
                    if (coinSet.size() > 1) {
                        String line = address + ": " + coinSet + " " + coinSet.size() + "次";
                        log.info(line);
                        DuplicateAddressRes duplicateAddressRes = new DuplicateAddressRes(address, coinSet);
                        duplicateAddressResList.add(duplicateAddressRes);
                    }
                });
                if (!ObjectUtils.isEmpty(duplicateAddressResList)) {
                    duplicateAddressResList.sort((o1, o2) -> o2.getCount() - o1.getCount());
                    String file = CommonConstant.FILE_BASE_PATH + CommonConstant.DUPLICATE_ADDRESS_RES_FILE + TimeUtils.getNowTimeLowLine(1) + ".txt";
                    FileUtils.writeTextOver(file, duplicateAddressResList);
                    log.info(CommonConstant.DUPLICATE_ADDRESS_RES_FILE + " --> " + file);
                }else {
                    log.info("检测无重复地址");
                }

            }
        }).headRowNumber(0).sheet().doRead();
    }


}
