package com.addresssign;

import com.addresssign.auth.Auth;
import com.addresssign.constant.CommonConstant;
import com.addresssign.process.DuplicateAddressCheck;
import com.addresssign.process.NewAddressCheck;
import com.blate.util.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
@Slf4j
@RequiredArgsConstructor
public class Door {

    private final DuplicateAddressCheck duplicateAddressCheck;
    private final NewAddressCheck newAddressCheck;

    public void openTheDoor() {
        /*
          上线前记得 调整版本即可
         */
        String version = "V4";

        Auth.checkAuth();
        start(version);
    }

    public void start(String version) {
        log.info("===================地址处理机器人开始启动（当前版本{}）===================", version);
        String config = FileUtils.getTxt(CommonConstant.FILE_BASE_PATH + CommonConstant.CONFIG_FILE);
        log.info("读取配置为：{}",config);
        switch (config) {
            case "1" -> {
                log.info("===================启动重复地址检测脚本===================");
                duplicateAddressCheck.start(CommonConstant.DUPLICATE_ADDRESS_SOURCE_FILE);
            }
            case "2" -> {
                log.info("===================启动新地址检测脚本===================");
                newAddressCheck.start(CommonConstant.NEW_ADDRESS_CHECK_SOURCE_FILE);
            }
            case "3" -> {
                log.info("===================先启动重复地址检测脚本再启动新地址检测脚本===================");
                duplicateAddressCheck.start(CommonConstant.DUPLICATE_ADDRESS_SOURCE_FILE);
                newAddressCheck.start(CommonConstant.NEW_ADDRESS_CHECK_SOURCE_FILE);
            }
            default -> {
                log.info("===================请输入1、2、3,其他指令不予处理，程序终止！===================");
                System.exit(0);
            }
        }
    }

    static {
        CommonConstant.FILE_BASE_PATH =  FileUtils.getBasePath();
        if(ObjectUtils.isEmpty(CommonConstant.FILE_BASE_PATH)){
            log.error("检测项目运行路径为空！运行失败");
            System.exit(0);
        }else {
            log.info("检测到项目运行路径:{}", CommonConstant.FILE_BASE_PATH);
        }
    }

}
