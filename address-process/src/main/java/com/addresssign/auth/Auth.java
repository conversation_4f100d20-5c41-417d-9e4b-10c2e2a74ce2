package com.addresssign.auth;


import com.blate.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class Auth {

    private static Boolean STOP_FLAG = false;

    public static void checkAuth() {
        String expireTime = "2026-12-31 06:00:00:00";
        if (System.currentTimeMillis() > TimeUtils.TimeToTimeCuo(expireTime, 2)) {
            STOP_FLAG = true;
        }
        executeStop();
    }

    public static void checkFileExist(String file) {
        if (!new File(file).exists()) {
            log.info("请检查文件是否存在：{}", file);
            STOP_FLAG = true;
        }
        executeStop();
    }

    public static void checkFileExist(String... fileList) {
        List<String> collect = Arrays.stream(fileList).collect(Collectors.toList());
        collect.forEach(file -> {
            if (!new File(file).exists()) {
                log.info("请检查文件是否存在：{}", file);
                STOP_FLAG = true;
            }
        });
        executeStop();
    }

    private static void executeStop() {
        if (STOP_FLAG) {
            System.exit(0);
        }
    }

}