DROP TABLE IF EXISTS smart_money;
-- 创建smart_money表 (PostgreSQL)
CREATE TABLE smart_money
(
    id                    BIGINT NOT NULL PRIMARY KEY,
    chain                 VARCHAR(50) COMMENT '钱包所在链',
    address               VARCHAR(100) COMMENT '钱包地址',
    balance               DECIMAL(30, 18) COMMENT '余额',
    total_profit          DECIMAL(30, 18) COMMENT '总的已实现利润 usdt',
    all_pnl               DECIMAL(10, 4) COMMENT '总的已实现利润比例 百分点',
    period VARCHAR (10) COMMENT '周期: 1d, 7d, 30d, all',

    -- 与当前周期相关的数据
    win_rate              DECIMAL(5, 2) COMMENT '当前周期胜率',
    history_bought_cost   DECIMAL(30, 18) COMMENT '当前周期代币总买入成本',
    realized_profit       DECIMAL(30, 18) COMMENT '当前周期已实现利润',
    pnl                   DECIMAL(10, 4) COMMENT '当前周期已实现利润比例',
    token_num             INTEGER COMMENT '当前周期交易过的代币数量',
    avg_holding_peroid    DECIMAL(15, 2) COMMENT '当前周期平均持仓时间 单位秒',

    -- 盈亏分布统计
    pnl_gt_5x_num         INTEGER COMMENT '盈利500点以上的代币数量',
    pnl_2x_5x_num         INTEGER COMMENT '盈利200-500点的代币数量',
    pnl_lt_2x_num         INTEGER COMMENT '盈利0-200点的代币数量',
    pnl_minus_dot5_0x_num INTEGER COMMENT '亏损50点以内的代币数量',
    pnl_lt_minus_dot5_num INTEGER COMMENT '亏损50点以上的代币数量',

    -- 交易次数统计
    buy                   INTEGER COMMENT '买的次数',
    sell                  INTEGER COMMENT '卖的次数',

    -- 系统字段
    create_time           TIMESTAMP COMMENT '创建时间',
    update_time           TIMESTAMP COMMENT '更新时间',
    creator               VARCHAR(255) COMMENT '创建人'
);

-- 创建索引
CREATE INDEX idx_smart_money_address ON smart_money (address);
CREATE INDEX idx_smart_money_chain ON smart_money (chain);
CREATE INDEX idx_smart_money_period ON smart_money (period);
CREATE INDEX idx_smart_money_create_time ON smart_money (create_time);