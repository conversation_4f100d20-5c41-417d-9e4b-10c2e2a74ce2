package com.gmgnbot.client;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.blate.dto.OkhttpResp;
import com.blate.util.OkhttpUtils;
import com.gmgnbot.dto.Coin;
import com.gmgnbot.dto.Holder;
import com.gmgnbot.dto.HolderBaseInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public class GmgnClient {

//    private static String baseUrl = "http://localhost:3000/api";
    private static String baseUrl = "http://***********:3000/api";

    private static String coinPath = "/coins";
    private static String holdersPath = "/holders";
    private static String holderBaseInfoPath = "/getWinRate";
    private static String tradePath = "/trade";

    public static List<Coin> getCoinList() {
//        OkhttpResp okhttpResp = OkhttpUtils.get(baseUrl + coinPath);
        OkhttpResp okhttpResp = OkhttpUtils.get(baseUrl + coinPath+"?orderby=holder_count&direction=asc");// BLATE-TODO 2025/5/19 支持排序
        String jsonString = okhttpResp.getJsonString();
        JSONObject data = JSONObject.parseObject(jsonString).getJSONObject("data");
        JSONArray rank = data.getJSONArray("rank");
        return rank.toJavaList(Coin.class);
    }

    public static List<Holder> getHolderList(String coin) {
        OkhttpResp okhttpResp = OkhttpUtils.get(baseUrl + holdersPath + "/" + coin);
        String jsonString = okhttpResp.getJsonString();
        JSONObject data = JSONObject.parseObject(jsonString).getJSONObject("data");
        JSONArray list = data.getJSONArray("list");
        return list.toJavaList(Holder.class);
    }

    public static HolderBaseInfo getHolderBaseInfo(String address) {
        OkhttpResp okhttpResp = OkhttpUtils.get(baseUrl + holderBaseInfoPath + "/" + address);
        String jsonString = okhttpResp.getJsonString();
        JSONObject data = JSONObject.parseObject(jsonString).getJSONObject("data");
        HolderBaseInfo holderBaseInfo = data.toJavaObject(HolderBaseInfo.class);
        holderBaseInfo.setAddress(address);
        return holderBaseInfo;
    }

    public static HolderBaseInfo getHolderTxList(String address) {
        OkhttpResp okhttpResp = OkhttpUtils.get(baseUrl + tradePath + "/" + address);
        String jsonString = okhttpResp.getJsonString();
        JSONObject data = JSONObject.parseObject(jsonString).getJSONObject("data");
        if (Objects.isNull(data)) {
            return null;
        }
        HolderBaseInfo holderBaseInfo = data.toJavaObject(HolderBaseInfo.class);
        holderBaseInfo.setAddress(address);
        return holderBaseInfo;
    }

    public static void main(String[] args) {

//        System.out.println(getCoinList());

        getHolderList();

//        System.out.println(getHolderBaseInfo("5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"));

//        System.out.println(getHolderTxList("EwTNPYTuwxMzrvL19nzBsSLXdAoEmVBKkisN87csKgtt"));

    }

    private static void getHolderList() {

        List<Holder> holderList = getHolderList("6hpfrxQb2kn9iprcJcZ6uXruGAwDe12W9zT4y4WSpump");
        holderList.forEach(holder -> {
            String address = holder.getAddress();
            HolderBaseInfo holderBaseInfo = getHolderBaseInfo(address);
            System.out.println(address+" "+holderBaseInfo.getWinrate());
        });

    }

}
