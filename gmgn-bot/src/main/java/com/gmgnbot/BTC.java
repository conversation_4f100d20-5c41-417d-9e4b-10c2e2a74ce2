package com.gmgnbot;

import org.bitcoinj.base.Address;
import org.bitcoinj.base.ScriptType;
import org.bitcoinj.core.NetworkParameters;
import org.bitcoinj.crypto.ChildNumber;
import org.bitcoinj.crypto.DeterministicHierarchy;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.params.MainNetParams;
import org.bitcoinj.params.TestNet3Params;
import org.bitcoinj.wallet.DeterministicSeed;
import org.bitcoinj.wallet.Wallet;

import java.util.Arrays;
import java.util.List;

public class BTC {

//    public static void main(String[] args) throws Exception {
//        // 助记词
//        String mnemonic = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about";
//
//        // 可选密码（BIP39 passphrase）
//        String passphrase = "";
//
//        // 网络参数：测试网
//        NetworkParameters params = TestNet3Params.get();
//
//        // 创建 DeterministicSeed
//        long creationTime = System.currentTimeMillis() / 1000;
//        DeterministicSeed seed = new DeterministicSeed(mnemonic, null, passphrase, creationTime);
//
//        // 使用 Wallet 从种子生成钱包（BIP44 默认路径 m/44'/1'/0'/0/0）
//        Wallet wallet = Wallet.fromSeed(params,seed, ScriptType.P2WPKH);
//
//        // 获取第一个接收地址（P2PKH）
//        System.out.println("Testnet address: " + wallet.currentReceiveAddress());
////        tb1pdsunq7dqcnhw6zgtwdhyakxs96shdzavf6uzx782hjx2mhzjfhsq9252pl
//    }

    public static void main(String[] args) throws Exception {
        // 助记词（12个词）
        String mnemonic = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about";
        String passphrase = ""; // 可选密码
        long creationTime = System.currentTimeMillis() / 1000;

        // 网络参数（Testnet）
//        NetworkParameters params = TestNet3Params.get();
        NetworkParameters params = MainNetParams.get();

        // 生成种子
        DeterministicSeed seed = new DeterministicSeed(mnemonic, null, passphrase, creationTime);
        byte[] seedBytes = seed.getSeedBytes();

        // 创建主密钥（master key）
        DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seedBytes);

        // BIP44 路径：m / 44' / 1' / 0' / 0 / 0
        // Hardened index = index | HARDENED_BIT
        final int HARDENED = ChildNumber.HARDENED_BIT;

        List<ChildNumber> bip44Path = Arrays.asList(
                new ChildNumber(86, true),   // purpose'
                new ChildNumber(0, true),    // coin_type' (1 = testnet)
                new ChildNumber(0, true),    // account'
                ChildNumber.ZERO,            // change = 0 (external)
                ChildNumber.ZERO             // address_index = 0
        );

        // 从主密钥依次派生路径
        DeterministicHierarchy hierarchy = new DeterministicHierarchy(masterKey);
        DeterministicKey key = hierarchy.get(bip44Path, true, true);

        // 生成 P2PKH 地址（m开头）
        Address address = Address.fromKey(params, key, ScriptType.P2WPKH);

        // 显示结果
        System.out.println("BIP44 Path: m/44'/1'/0'/0/0");
        System.out.println("Testnet Address: " + address);
        System.out.println("Private Key (WIF): " + key.getPrivateKeyAsWiF(params));
    }

}
