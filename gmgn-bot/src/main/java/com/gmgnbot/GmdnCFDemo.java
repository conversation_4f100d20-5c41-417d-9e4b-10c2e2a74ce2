package com.gmgnbot;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.blate.dto.OkhttpResp;
import com.blate.dto.ProxyInfo;
import com.blate.util.FileUtils;
import com.blate.util.OkhttpUtils;
import com.blate.util.ProxyUtil;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;

public class GmdnCFDemo {


    private static final String clientKey = "ea9ce9b707e7f017d9f5ba58810aace3";

    public static void main(String[] args) {

//        base();

        String websiteUrl = "https://gmgn.ai/defi/quotation/v1/tokens/top_holders/sol/6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump?device_id=eb7a0ccd-410d-453d-af3e-7b299f9bc70c&client_id=gmgn_web_2025.0124.173246&from_app=gmgn&app_ver=2025.0124.173246&tz_name=Asia%2FShanghai&tz_offset=28800&app_lang=en&limit=20&cost=20&orderby=profit&direction=desc";
        OkhttpResp resp = OkhttpUtils.get(websiteUrl);
        if (resp.getCode() == 403) {
            String websiteKey = "0x4AAAAAAAAjq6WYeRDKmebM";
            OkhttpResp task = createTask(websiteUrl, websiteKey, base64(resp.getJsonString()));
            if (task.getCode() == 200) {
                JSONObject parse = JSON.parseObject(task.getJsonString());
                String taskId = parse.getString("taskId");
                while (true) {
                    getTaskResult(taskId);
//                    OkhttpUtils.get(websiteUrl);
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
//                    getTaskResult(taskId);
                }

            }

        }

//        createTask();

//        1277913736
////        2091870958
//
//        getTaskResult("1277913736");
//        getTaskResult("2091870958");

    }

    private static void base() {
        String txt = FileUtils.getTxt("/Users/<USER>/disk/projects/black-eight/exchanges-bot/1.html");
        String htmlPageBase64 = base64(txt);
        System.out.println(htmlPageBase64);
    }

    private static void a() {
        String ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36";
        OkhttpResp okhttpResp = OkhttpUtils.get("https://capmonster.cloud/api/useragent/actual");
        System.out.println(okhttpResp.getJsonString());
    }

    private static OkhttpResp createTask(String websiteUrl, String websiteKey, String htmlPageBase64) {
        String url = "https://api.capmonster.cloud/createTask";
        String ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36";
        HashMap<String, String> header = new HashMap<>();
        header.put("User-Agent", ua);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("clientKey", clientKey);

        JSONObject task = new JSONObject();
        task.put("type", "TurnstileTask");
        task.put("websiteURL", websiteUrl);
        task.put("websiteKey", websiteKey);
        task.put("cloudflareTaskType", "cloudflareTaskType");
        task.put("userAgent", ua);
        task.put("htmlPageBase64", htmlPageBase64);

        ProxyInfo proxy = ProxyUtil.getProxy();

        task.put("proxyType", "http");
        task.put("proxyAddress", proxy.getHost());
        task.put("proxyPort", proxy.getPort());
        task.put("proxyLogin", "");
        task.put("proxyPassword", "");



        jsonObject.put("task", task);
        jsonObject.put("callbackUrl", "");
        return OkhttpUtils.post(url, jsonObject, header);
    }

    private static void getTaskResult(String taskId) {
        String url = "https://api.capmonster.cloud/getTaskResult";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("clientKey", clientKey);
        jsonObject.put("taskId", taskId);
        OkhttpUtils.post(url, jsonObject);
    }

    private static String base64(String html) {
        String base64 = "";
        try {
            base64 = java.util.Base64.getEncoder().encodeToString(html.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return base64;
    }

}
