package com.gmgnbot;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"com.gmgnbot", "com.blate"})
public class GmgnBotApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext run = SpringApplication.run(GmgnBotApplication.class, args);
        SmartAddressModel smartAddressModel = run.getBean(SmartAddressModel.class);
        smartAddressModel.start();
    }

}
