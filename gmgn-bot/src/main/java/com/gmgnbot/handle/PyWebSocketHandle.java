package com.gmgnbot.handle;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;

@EnableWebSocket
@Component
@Slf4j
public class PyWebSocketHandle extends TextWebSocketHandler {
    public static final String BASE_URL = "ws://127.0.0.1:8000/ws";
    private WebSocketSession pySession;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        this.pySession = session;
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) {
        String payload = (String) message.getPayload();
        log.info("获取消息 {}", payload);
        sendMessage(session, payload);
        //等1秒
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }

    public void sendMessage(WebSocketSession session, String message) {
        try {
            session.sendMessage(new TextMessage(message));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void sendMessageToPy(String message) {
        try {
            pySession.sendMessage(new TextMessage(message));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
