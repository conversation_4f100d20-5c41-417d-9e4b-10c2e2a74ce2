package com.gmgnbot.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("smart_money")
public class SmartMoney {
    //跟周期无关的数据
    private Long id;//id
    private String chain;//钱包所在链
    private String address;//钱包地址
    private BigDecimal balance;//余额
    private BigDecimal total_profit;//总的 已实现利润 usdt
    private BigDecimal all_pnl;//总的 已实现利润比例 百分点
    private String period; // "1d", "7d", "30d", "all"

    //跟 当前周期 有关的数据
    private BigDecimal winRate;//当前周期胜率（通过计算得到）
    private BigDecimal history_bought_cost;//当前周期 代币总买入成本
    private BigDecimal realized_profit;//当前周期 已实现利润
    private BigDecimal pnl;//当前周期 已实现利润比例
    private Integer token_num;//当前周期 交易过的代币数量
    private BigDecimal avg_holding_peroid;//当前周期 平均持仓时间 单位秒

    private Integer pnl_gt_5x_num;//盈利 500 点以上 的代币数量
    private Integer pnl_2x_5x_num;//盈利 200-500 点以上 的代币数量
    private Integer pnl_lt_2x_num;//盈利 0-200 点以内 的代币数量
    private Integer pnl_minus_dot5_0x_num;//亏损 50点以内 的代币数量
    private Integer pnl_lt_minus_dot5_num;//亏损 50点以上 的代币数量

    private Integer buy;//买的次数
    private Integer sell;//卖的次数


    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    private String creator;
}