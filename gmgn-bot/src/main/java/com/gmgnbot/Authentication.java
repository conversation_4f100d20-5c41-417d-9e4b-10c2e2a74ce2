package com.gmgnbot;

import com.warrenstrange.googleauth.GoogleAuthenticator;
import org.bitcoinj.base.Address;
import org.bitcoinj.base.ScriptType;
import org.bitcoinj.core.NetworkParameters;
import org.bitcoinj.crypto.ChildNumber;
import org.bitcoinj.crypto.DeterministicHierarchy;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.params.MainNetParams;
import org.bitcoinj.wallet.DeterministicSeed;

import java.util.Arrays;
import java.util.List;

public class Authentication {

    public static void main(String[] args) {
//        String secretKey = "7EMMRHB45VIDPKNK";
        String secretKey = "4N6T7XN3GK5U6J3N";
        while (true){
            try {
                System.out.println("当前验证码: " + getCode(secretKey));
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }

    private static int getCode(String secretKey){
        GoogleAuthenticator gAuth = new GoogleAuthenticator();
        return gAuth.getTotpPassword(secretKey);
    }

}
