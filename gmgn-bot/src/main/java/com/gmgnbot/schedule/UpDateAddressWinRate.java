package com.gmgnbot.schedule;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gmgnbot.client.GmgnClient;
import com.gmgnbot.dto.HolderBaseInfo;
import com.gmgnbot.entity.SmartMoney;
import com.gmgnbot.service.SmartMoneyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class UpDateAddressWinRate {

    @Resource
    private SmartMoneyServiceImpl smartMoneyService;

    @Scheduled(cron = "0/30 * * * * ?")
    public void updateWinRate() {

        List<SmartMoney> list = smartMoneyService.list(new QueryWrapper<SmartMoney>().isNull("win_rate").or().eq("win_rate", ""));

        if (list.isEmpty()) {
            log.info("暂无需要更新胜率的数据");
            return;
        }
        for (SmartMoney smartMoney : list) {
            try {
                HolderBaseInfo holderBaseInfo = GmgnClient.getHolderBaseInfo(smartMoney.getAddress());
                String winRate = holderBaseInfo.getWinrate();
                smartMoney.setWinRate(winRate);
                smartMoney.setPnl_1d(holderBaseInfo.getPnl_1d());
                smartMoney.setPnl_7d(holderBaseInfo.getPnl_7d());
                smartMoney.setPnl_30d(holderBaseInfo.getPnl_30d());
                smartMoney.setAll_pnl(holderBaseInfo.getAll_pnl());
                smartMoneyService.update(smartMoney,new QueryWrapper<SmartMoney>().eq("address", smartMoney.getAddress()));
                log.info("已更新地址 {} 的胜率为 {}", smartMoney.getAddress(), winRate);
            } catch (Exception e) {
                log.error("更新地址 {} 胜率失败：{}", smartMoney.getAddress(), e.getMessage(), e);
            }
        }
    }

}
