package com.gmgnbot.schedule;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gmgnbot.client.GmgnClient;
import com.gmgnbot.dto.Coin;
import com.gmgnbot.dto.Holder;
import com.gmgnbot.entity.SmartMoney;
import com.gmgnbot.service.SmartMoneyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class FetchAddress {

    @Autowired
    @Qualifier("defaultThreadPool")
    private ThreadPoolTaskExecutor defaultThreadPool;

    @Resource
    private SmartMoneyServiceImpl smartMoneyService;

    private static final long coinMaxSize = 1;
    private static final long coinHolderMaxSize = 1;

    //    @Scheduled(cron = "0 0/1 * * * ?")
    @Scheduled(cron = "0/1 * * * * ?")
    public void updateWinRate() {

//        1.获取所有coin
        List<Coin> coinList = GmgnClient.getCoinList().stream().limit(coinMaxSize).toList();
        log.info("热门代币 爬取完毕,数量：{}", coinList.size());

//         2. 为每个 Coin 并发拉 Holder 列表
        List<CompletableFuture<List<Holder>>> holderFutures = coinList.stream().map(coin ->
                CompletableFuture.supplyAsync(() -> {
                    String threadName = Thread.currentThread().getName();
                    String coinAddress = coin.getAddress();
                    log.info("线程 [" + threadName + "] 正在拉取 holder，coin 地址: " + coinAddress);
                    return GmgnClient.getHolderList(coinAddress).stream().limit(coinHolderMaxSize).peek(holder -> holder.setChain(coin.getChain())).toList();
                }, defaultThreadPool)
        ).toList();

//         3.等待所有 Coin 的 holders 拉取完成
        List<List<Holder>> allHolders = holderFutures.stream().map(CompletableFuture::join).toList();

        allHolders.forEach(holderList -> {
            holderList.forEach(holder -> {
                SmartMoney smartMoney = new SmartMoney();
                smartMoney.setChain(holder.getChain());
                smartMoney.setAddress(holder.getAddress());
                smartMoneyService.saveOrUpdate(smartMoney, new QueryWrapper<SmartMoney>().eq("address", holder.getAddress()));
            });
        });

        log.info("holders 爬取完毕,数量：{}", allHolders.size());
    }

}
