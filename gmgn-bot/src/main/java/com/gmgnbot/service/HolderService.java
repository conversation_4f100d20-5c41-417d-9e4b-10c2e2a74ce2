package com.gmgnbot.service;

import com.gmgnbot.client.GmgnClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class HolderService {

    @Async("defaultThreadPool")
    public void fetchHolderInfo(String address) {
        try {
            var holders = GmgnClient.getHolderList(address);
            System.out.println("✅ " + address + " 获取成功，数量：" + holders.size());
        } catch (Exception e) {
            System.err.println("❌ 获取失败：" + address + "，错误：" + e.getMessage());
        }
    }
}
