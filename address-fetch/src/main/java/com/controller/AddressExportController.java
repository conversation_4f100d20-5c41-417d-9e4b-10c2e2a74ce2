//package com.controller;
//
//import com.blate.reponse.Resp;
//import com.dto.AddressExportDTO;
//import com.dto.ExportRequest;
//import com.service.AddressService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.io.OutputStream;
//import java.net.URLEncoder;
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//
///**
// * 地址导出控制器
// */
//@RestController
//@RequestMapping("/api/address/export")
//@Slf4j
//@RequiredArgsConstructor
//public class AddressExportController {
//
//    private final AddressService addressService;
//
//    /**
//     * 异步导出地址数据
//     */
//    @PostMapping("/async")
//    public Resp<String> exportAsync(@RequestBody ExportRequest request) {
//        try {
//            String batchId = addressService.exportAddressesAsync(request);
//            return Resp.success(batchId, "导出任务已提交，请通过批次ID查询进度");
//        } catch (Exception e) {
//            log.error("异步导出失败", e);
//            return Resp.error("导出失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 同步导出地址数据
//     */
//    @PostMapping("/sync")
//    public Resp<String> exportSync(@RequestBody ExportRequest request) {
//        try {
//            String filePath = addressService.exportAddressesSync(request);
//            return Resp.success(filePath, "导出成功");
//        } catch (Exception e) {
//            log.error("同步导出失败", e);
//            return Resp.error("导出失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 查询导出进度
//     */
//    @GetMapping("/progress/{batchId}")
//    public Resp<ExportRecord> getExportProgress(@PathVariable String batchId) {
//        try {
//            ExportRecord record = addressService.getExportRecord(batchId);
//            if (record == null) {
//                return Resp.error("导出记录不存在");
//            }
//            return Resp.success(record);
//        } catch (Exception e) {
//            log.error("查询导出进度失败", e);
//            return Resp.error("查询失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 下载导出文件
//     */
//    @GetMapping("/download/{batchId}")
//    public void downloadFile(@PathVariable String batchId, HttpServletResponse response) {
//        try {
//            ExportRecord record = addressService.getExportRecord(batchId);
//            if (record == null || record.getExportStatus() != ExportRecord.ExportStatus.SUCCESS.getCode()) {
//                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
//                return;
//            }
//
//            File file = new File(record.getFilePath());
//            if (!file.exists()) {
//                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
//                return;
//            }
//
//            // 设置响应头
//            response.setContentType("application/octet-stream");
//            response.setContentLengthLong(file.length());
//            response.setHeader("Content-Disposition",
//                    "attachment; filename=" + URLEncoder.encode(record.getFileName(), StandardCharsets.UTF_8));
//
//            // 输出文件
//            try (FileInputStream fis = new FileInputStream(file);
//                 OutputStream os = response.getOutputStream()) {
//
//                byte[] buffer = new byte[8192];
//                int bytesRead;
//                while ((bytesRead = fis.read(buffer)) != -1) {
//                    os.write(buffer, 0, bytesRead);
//                }
//                os.flush();
//            }
//
//        } catch (IOException e) {
//            log.error("下载文件失败", e);
//            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
//        }
//    }
//
//    /**
//     * 根据区间查询地址列表
//     */
//    @GetMapping("/addresses/by-range")
//    public Resp<List<AddressExportDTO>> getAddressesByRange(
//            @RequestParam List<String> rangeNames,
//            @RequestParam(required = false) List<String> chains,
//            @RequestParam(required = false, defaultValue = "false") Boolean onlyFirstReceive) {
//        try {
//            List<AddressExportDTO> addresses = addressService.getAddressesByRange(rangeNames, chains, onlyFirstReceive);
//            return Resp.success(addresses);
//        } catch (Exception e) {
//            log.error("查询地址列表失败", e);
//            return Resp.error("查询失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 查询导出记录列表
//     */
//    @GetMapping("/records")
//    public Resp<List<ExportRecord>> getExportRecords(
//            @RequestParam(required = false) String creator,
//            @RequestParam(required = false) Integer exportType,
//            @RequestParam(defaultValue = "1") Integer pageNum,
//            @RequestParam(defaultValue = "20") Integer pageSize) {
//        try {
//            List<ExportRecord> records = addressService.getExportRecords(creator, exportType, pageNum, pageSize);
//            return Resp.success(records);
//        } catch (Exception e) {
//            log.error("查询导出记录失败", e);
//            return Resp.error("查询失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取区间统计信息
//     */
//    @GetMapping("/statistics")
//    public Resp<List<Object>> getRangeStatistics() {
//        try {
//            List<Object> statistics = addressService.getRangeStatistics();
//            return Resp.success(statistics);
//        } catch (Exception e) {
//            log.error("查询统计信息失败", e);
//            return Resp.error("查询失败: " + e.getMessage());
//        }
//    }
//}
