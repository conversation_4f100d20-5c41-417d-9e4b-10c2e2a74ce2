package com.controller;

import com.blate.reponse.Resp;
import com.client.OkxClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Slf4j
@RequiredArgsConstructor
public class Controller {

    @GetMapping("/updateOkxApiKey")
    public Resp updateOkxApiKey(String apiKey) {
        try {
            // 参数验证
            if (apiKey == null || apiKey.trim().isEmpty()) {
                return Resp.fail("API Key 不能为空");
            }

            // 更新API Key
            OkxClient.updateApiKey(apiKey.trim());

            // 获取当前API Key进行确认
            String currentApiKey = OkxClient.getCurrentApiKey();

            log.info("OKX API Key 已更新: {}", maskApi<PERSON>ey(currentApiKey));
            return Resp.success(maskApi<PERSON>ey(currentApiKey), "API Key 更新成功");

        } catch (Exception e) {
            log.error("更新 OKX API Key 失败", e);
            return Resp.fail("更新失败: " + e.getMessage());
        }
    }

    /**
     * 脱敏显示API Key
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
//        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
        return apiKey;
    }

}
