package com.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 地址交易记录日志表
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("address_transaction_log")
public class AddressTransactionLog {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 钱包地址
     */
    private String address;
    
    /**
     * 区块链网络
     */
    private String chain;
    
    /**
     * 交易哈希
     */
    private String transactionHash;
    
    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;
    
    /**
     * 交易类型 1-收款 2-转账
     */
    private Integer transactionType;
    
    /**
     * 命中的金额区间
     */
    private String amountRangeMatched;
    
    /**
     * 是否命中区间 0-否 1-是
     */
    private Integer isRangeMatched;
    
    /**
     * 是否首次收款
     */
    private Integer isFirstReceive;
    
    /**
     * API检查结果JSON
     */
    private String apiCheckResult;
    
    /**
     * 处理状态 0-待处理 1-已处理 2-处理失败
     */
    private Integer processStatus;
    
    /**
     * 处理时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
