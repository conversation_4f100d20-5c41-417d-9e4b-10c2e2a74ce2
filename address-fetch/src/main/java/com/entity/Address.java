package com.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 地址主表
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("address")
public class Address {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 钱包地址
     */
    private String to;

    /**
     * 金额
     */
    private BigDecimal changeAmount;
    
    /**
     * 命中的金额区间(需要设置索引，加速查询导出)
     */
    private String amountRangeMatched;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
}
