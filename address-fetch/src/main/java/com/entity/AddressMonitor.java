package com.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 地址监控主表
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("address_monitor")
public class AddressMonitor {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 钱包地址
     */
    private String address;
    
    /**
     * 区块链网络
     */
    private String chain;
    
    /**
     * 最新交易金额
     */
    private BigDecimal transactionAmount;
    
    /**
     * 命中的金额区间
     */
    private String amountRangeMatched;
    
    /**
     * 是否首次收款 0-否 1-是
     */
    private Integer isFirstReceive;
    
    /**
     * 首次收款时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime firstReceiveTime;
    
    /**
     * 首次收款金额
     */
    private BigDecimal firstReceiveAmount;
    
    /**
     * 监控状态 0-停止 1-监控中
     */
    private Integer monitorStatus;
    
    /**
     * 最后检查时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastCheckTime;
    
    /**
     * 检查次数
     */
    private Integer checkCount;
    
    /**
     * 总交易次数
     */
    private Integer totalTransactions;
    
    /**
     * 总交易金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 命中区间次数
     */
    private Integer matchedCount;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
}
