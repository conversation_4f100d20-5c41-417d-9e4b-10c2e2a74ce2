package com.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dto.AddressExportDTO;
import com.entity.AddressRangeSummary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 地址区间统计Mapper
 */
@Mapper
public interface AddressRangeSummaryMapper extends BaseMapper<AddressRangeSummary> {
    
    /**
     * 根据区间查询地址列表
     */
    @Select("<script>" +
            "SELECT * FROM v_address_export_detail WHERE 1=1 " +
            "<if test='rangeNames != null and rangeNames.size() > 0'>" +
            "  AND range_name IN " +
            "  <foreach collection='rangeNames' item='rangeName' open='(' separator=',' close=')'>" +
            "    #{rangeName}" +
            "  </foreach>" +
            "</if>" +
            "<if test='chains != null and chains.size() > 0'>" +
            "  AND chain IN " +
            "  <foreach collection='chains' item='chain' open='(' separator=',' close=')'>" +
            "    #{chain}" +
            "  </foreach>" +
            "</if>" +
            "<if test='onlyFirstReceive != null and onlyFirstReceive == true'>" +
            "  AND is_first_receive_in_range = 1" +
            "</if>" +
            "ORDER BY first_hit_time DESC" +
            "</script>")
    List<AddressExportDTO> selectByRange(@Param("rangeNames") List<String> rangeNames,
                                       @Param("chains") List<String> chains,
                                       @Param("onlyFirstReceive") Boolean onlyFirstReceive);
    
    /**
     * 根据时间范围查询地址列表
     */
    @Select("<script>" +
            "SELECT * FROM v_address_export_detail WHERE 1=1 " +
            "<if test='startTime != null and startTime != \"\"'>" +
            "  AND first_hit_time >= #{startTime}" +
            "</if>" +
            "<if test='endTime != null and endTime != \"\"'>" +
            "  AND first_hit_time <= #{endTime}" +
            "</if>" +
            "<if test='chains != null and chains.size() > 0'>" +
            "  AND chain IN " +
            "  <foreach collection='chains' item='chain' open='(' separator=',' close=')'>" +
            "    #{chain}" +
            "  </foreach>" +
            "</if>" +
            "ORDER BY first_hit_time DESC" +
            "</script>")
    List<AddressExportDTO> selectByTime(@Param("startTime") String startTime,
                                      @Param("endTime") String endTime,
                                      @Param("chains") List<String> chains);
    
    /**
     * 查询所有地址用于全量导出
     */
    @Select("SELECT * FROM v_address_export_detail ORDER BY first_hit_time DESC")
    List<AddressExportDTO> selectAllForExport();
    
    /**
     * 查询区间统计信息
     */
    @Select("SELECT * FROM v_address_range_stats ORDER BY range_name")
    List<Object> selectRangeStatistics();
    
    /**
     * 根据区间名称统计地址数量
     */
    @Select("SELECT range_name, COUNT(DISTINCT address) as address_count, " +
            "SUM(hit_count) as total_hits, SUM(total_amount) as total_amount " +
            "FROM address_range_summary " +
            "WHERE range_name = #{rangeName} " +
            "GROUP BY range_name")
    Object selectRangeStatByName(@Param("rangeName") String rangeName);
}
