package com.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.entity.Address;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface AddressMapper extends BaseMapper<Address> {

    /**
     * 根据地址查询
     */
    List<Address> selectByAddress(@Param("address") String address);

    /**
     * 根据金额区间查询地址列表
     */
    List<Address> selectByAmountRange(@Param("amountRange") String amountRange);

    /**
     * 根据多个金额区间查询地址列表
     */
    List<Address> selectByAmountRanges(@Param("amountRanges") List<String> amountRanges);

    /**
     * 根据金额范围查询
     */
    List<Address> selectByAmountBetween(@Param("minAmount") BigDecimal minAmount,
                                       @Param("maxAmount") BigDecimal maxAmount);

    /**
     * 根据时间范围查询
     */
    List<Address> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime,
                                   @Param("amountRange") String amountRange);

    /**
     * 分页查询地址列表
     */
    List<Address> selectPageList(@Param("address") String address,
                                @Param("amountRange") String amountRange,
                                @Param("minAmount") BigDecimal minAmount,
                                @Param("maxAmount") BigDecimal maxAmount,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime,
                                @Param("offset") Integer offset,
                                @Param("pageSize") Integer pageSize);

    /**
     * 统计地址总数
     */
    Long countByCondition(@Param("address") String address,
                         @Param("amountRange") String amountRange,
                         @Param("minAmount") BigDecimal minAmount,
                         @Param("maxAmount") BigDecimal maxAmount,
                         @Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);

    /**
     * 按金额区间统计地址数量
     */
    List<Map<String, Object>> countByAmountRange(@Param("amountRanges") List<String> amountRanges,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询重复地址
     */
    List<Map<String, Object>> selectDuplicateAddresses();

    /**
     * 批量插入地址
     */
    int batchInsert(@Param("list") List<Address> addresses);

    /**
     * 批量更新金额区间
     */
    int batchUpdateAmountRange(@Param("ids") List<Long> ids,
                              @Param("newAmountRange") String newAmountRange);

    /**
     * 根据地址删除
     */
    int deleteByAddress(@Param("address") String address);

    /**
     * 根据金额区间删除
     */
    int deleteByAmountRange(@Param("amountRange") String amountRange);

    /**
     * 清理过期数据
     */
    int deleteExpiredData(@Param("expireTime") LocalDateTime expireTime,
                         @Param("amountRange") String amountRange);
}
