package com.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 地址导出DTO
 */
@Data
@Accessors(chain = true)
public class AddressExportDTO {
    
    /**
     * 钱包地址
     */
    private String address;
    
    /**
     * 区块链网络
     */
    private String chain;
    
    /**
     * 金额区间名称
     */
    private String rangeName;
    
    /**
     * 区间最小金额
     */
    private BigDecimal rangeMin;
    
    /**
     * 区间最大金额
     */
    private BigDecimal rangeMax;
    
    /**
     * 命中该区间次数
     */
    private Integer hitCount;
    
    /**
     * 该区间总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 该区间平均金额
     */
    private BigDecimal avgAmount;
    
    /**
     * 首次命中时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime firstHitTime;
    
    /**
     * 最后命中时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastHitTime;
    
    /**
     * 在该区间是否有首次收款
     */
    private Integer isFirstReceiveInRange;
    
    /**
     * 该区间首次收款金额
     */
    private BigDecimal firstReceiveAmountInRange;
    
    /**
     * 该区间首次收款时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime firstReceiveTimeInRange;
    
    /**
     * 监控状态
     */
    private Integer monitorStatus;
    
    /**
     * 总交易次数
     */
    private Integer totalTransactions;
    
    /**
     * 命中区间次数
     */
    private Integer matchedCount;
}
