package com.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 导出请求DTO
 */
@Data
@Accessors(chain = true)
public class ExportRequest {
    
    /**
     * 导出类型 1-按区间 2-按时间 3-全量
     */
    private Integer exportType;
    
    /**
     * 导出的区间名称列表
     */
    private List<String> rangeNames;
    
    /**
     * 导出开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    
    /**
     * 导出结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    
    /**
     * 区块链网络过滤
     */
    private List<String> chains;
    
    /**
     * 是否只导出首次收款地址
     */
    private Boolean onlyFirstReceive;
    
    /**
     * 最小命中次数
     */
    private Integer minHitCount;
    
    /**
     * 最大命中次数
     */
    private Integer maxHitCount;
    
    /**
     * 导出文件格式 excel/csv
     */
    private String fileFormat;
    
    /**
     * 导出文件名前缀
     */
    private String fileNamePrefix;
    
    /**
     * 是否包含详细统计信息
     */
    private Boolean includeStats;
}
