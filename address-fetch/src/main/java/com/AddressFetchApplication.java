package com;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"com", "com.blate"})
public class AddressFetchApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext run = SpringApplication.run(AddressFetchApplication.class, args);
    }
}