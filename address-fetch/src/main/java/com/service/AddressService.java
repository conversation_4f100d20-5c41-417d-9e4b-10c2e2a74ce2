package com.service;

import com.dto.AddressExportDTO;
import com.dto.ExportRequest;
import com.entity.ExportRecord;

import java.util.List;

/**
 * 地址导出服务接口
 */
public interface AddressExportService {
    
    /**
     * 异步导出地址数据
     * @param request 导出请求
     * @return 导出批次ID
     */
    String exportAddressesAsync(ExportRequest request);
    
    /**
     * 同步导出地址数据
     * @param request 导出请求
     * @return 导出文件路径
     */
    String exportAddressesSync(ExportRequest request);
    
    /**
     * 根据区间名称查询地址列表
     * @param rangeNames 区间名称列表
     * @param chains 区块链网络列表
     * @param onlyFirstReceive 是否只查询首次收款地址
     * @return 地址列表
     */
    List<AddressExportDTO> getAddressesByRange(List<String> rangeNames, List<String> chains, Boolean onlyFirstReceive);
    
    /**
     * 根据时间范围查询地址列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param chains 区块链网络列表
     * @return 地址列表
     */
    List<AddressExportDTO> getAddressesByTime(String startTime, String endTime, List<String> chains);
    
    /**
     * 查询导出记录
     * @param batchId 批次ID
     * @return 导出记录
     */
    ExportRecord getExportRecord(String batchId);
    
    /**
     * 查询导出记录列表
     * @param creator 创建人
     * @param exportType 导出类型
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 导出记录列表
     */
    List<ExportRecord> getExportRecords(String creator, Integer exportType, Integer pageNum, Integer pageSize);
    
    /**
     * 获取区间统计信息
     * @return 区间统计信息
     */
    List<Object> getRangeStatistics();
    
    /**
     * 更新地址区间统计
     * @param address 地址
     * @param chain 区块链网络
     * @param rangeName 区间名称
     * @param amount 交易金额
     * @param isFirstReceive 是否首次收款
     */
    void updateAddressRangeSummary(String address, String chain, String rangeName, 
                                 String amount, Boolean isFirstReceive);
}
