package com.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.blate.util.UidGenerator;
import com.dto.AddressExportDTO;
import com.dto.ExportRequest;
import com.entity.AddressRangeSummary;
import com.entity.ExportRecord;
import com.mapper.AddressRangeSummaryMapper;
import com.mapper.ExportRecordMapper;
import com.service.AddressExportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 地址导出服务实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AddressExportServiceImpl implements AddressExportService {
    
    private final AddressRangeSummaryMapper addressRangeSummaryMapper;
    private final ExportRecordMapper exportRecordMapper;
    
    private static final String EXPORT_BASE_PATH = "/data/exports/";
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    @Override
    @Async
    public String exportAddressesAsync(ExportRequest request) {
        String batchId = UidGenerator.generate();
        
        // 创建导出记录
        ExportRecord exportRecord = new ExportRecord()
                .setBatchId(batchId)
                .setExportType(request.getExportType())
                .setRangeNames(JSON.toJSONString(request.getRangeNames()))
                .setStartTime(request.getStartTime())
                .setEndTime(request.getEndTime())
                .setFilterConditions(JSON.toJSONString(request))
                .setExportStatus(ExportRecord.ExportStatus.IN_PROGRESS.getCode())
                .setCreator("system");
        
        exportRecordMapper.insert(exportRecord);
        
        // 异步执行导出
        CompletableFuture.runAsync(() -> {
            try {
                String filePath = doExport(request, batchId);
                
                // 更新导出记录
                File file = new File(filePath);
                exportRecord.setFilePath(filePath)
                        .setFileName(file.getName())
                        .setFileSize(file.length())
                        .setExportStatus(ExportRecord.ExportStatus.SUCCESS.getCode())
                        .setCompleteTime(LocalDateTime.now());
                
                exportRecordMapper.updateById(exportRecord);
                
            } catch (Exception e) {
                log.error("导出失败, batchId: {}", batchId, e);
                
                exportRecord.setExportStatus(ExportRecord.ExportStatus.FAILED.getCode())
                        .setErrorMessage(e.getMessage())
                        .setCompleteTime(LocalDateTime.now());
                
                exportRecordMapper.updateById(exportRecord);
            }
        });
        
        return batchId;
    }
    
    @Override
    public String exportAddressesSync(ExportRequest request) {
        String batchId = UidGenerator.generate();
        return doExport(request, batchId);
    }
    
    private String doExport(ExportRequest request, String batchId) {
        // 查询数据
        List<AddressExportDTO> addresses = queryAddresses(request);
        
        // 生成文件名
        String fileName = generateFileName(request, batchId);
        String filePath = EXPORT_BASE_PATH + fileName;
        
        // 确保目录存在
        File directory = new File(EXPORT_BASE_PATH);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        // 导出到Excel
        if ("csv".equalsIgnoreCase(request.getFileFormat())) {
            // TODO: 实现CSV导出
            throw new UnsupportedOperationException("CSV导出暂未实现");
        } else {
            EasyExcel.write(filePath, AddressExportDTO.class)
                    .sheet("地址数据")
                    .doWrite(addresses);
        }
        
        log.info("导出完成, 文件路径: {}, 数据量: {}", filePath, addresses.size());
        return filePath;
    }
    
    private List<AddressExportDTO> queryAddresses(ExportRequest request) {
        switch (request.getExportType()) {
            case 1: // 按区间
                return getAddressesByRange(request.getRangeNames(), request.getChains(), request.getOnlyFirstReceive());
            case 2: // 按时间
                return getAddressesByTime(
                        request.getStartTime() != null ? request.getStartTime().toString() : null,
                        request.getEndTime() != null ? request.getEndTime().toString() : null,
                        request.getChains()
                );
            case 3: // 全量
                return addressRangeSummaryMapper.selectAllForExport();
            default:
                throw new IllegalArgumentException("不支持的导出类型: " + request.getExportType());
        }
    }
    
    private String generateFileName(ExportRequest request, String batchId) {
        StringBuilder fileName = new StringBuilder();
        
        if (StringUtils.hasText(request.getFileNamePrefix())) {
            fileName.append(request.getFileNamePrefix()).append("_");
        } else {
            fileName.append("address_export_");
        }
        
        fileName.append(LocalDateTime.now().format(DATE_FORMAT))
                .append("_")
                .append(batchId.substring(0, 8));
        
        String extension = "csv".equalsIgnoreCase(request.getFileFormat()) ? ".csv" : ".xlsx";
        fileName.append(extension);
        
        return fileName.toString();
    }
    
    @Override
    public List<AddressExportDTO> getAddressesByRange(List<String> rangeNames, List<String> chains, Boolean onlyFirstReceive) {
        return addressRangeSummaryMapper.selectByRange(rangeNames, chains, onlyFirstReceive);
    }
    
    @Override
    public List<AddressExportDTO> getAddressesByTime(String startTime, String endTime, List<String> chains) {
        return addressRangeSummaryMapper.selectByTime(startTime, endTime, chains);
    }
    
    @Override
    public ExportRecord getExportRecord(String batchId) {
        QueryWrapper<ExportRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("batch_id", batchId);
        return exportRecordMapper.selectOne(wrapper);
    }
    
    @Override
    public List<ExportRecord> getExportRecords(String creator, Integer exportType, Integer pageNum, Integer pageSize) {
        QueryWrapper<ExportRecord> wrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(creator)) {
            wrapper.eq("creator", creator);
        }
        if (exportType != null) {
            wrapper.eq("export_type", exportType);
        }
        
        wrapper.orderByDesc("create_time");
        
        if (pageNum != null && pageSize != null) {
            int offset = (pageNum - 1) * pageSize;
            wrapper.last("LIMIT " + offset + ", " + pageSize);
        }
        
        return exportRecordMapper.selectList(wrapper);
    }
    
    @Override
    public List<Object> getRangeStatistics() {
        return addressRangeSummaryMapper.selectRangeStatistics();
    }
    
    @Override
    public void updateAddressRangeSummary(String address, String chain, String rangeName, 
                                        String amount, Boolean isFirstReceive) {
        // 查询是否已存在记录
        QueryWrapper<AddressRangeSummary> wrapper = new QueryWrapper<>();
        wrapper.eq("address", address)
               .eq("chain", chain)
               .eq("range_name", rangeName);
        
        AddressRangeSummary summary = addressRangeSummaryMapper.selectOne(wrapper);
        BigDecimal amountDecimal = new BigDecimal(amount);
        
        if (summary == null) {
            // 创建新记录
            summary = new AddressRangeSummary()
                    .setAddress(address)
                    .setChain(chain)
                    .setRangeName(rangeName)
                    .setHitCount(1)
                    .setFirstHitTime(LocalDateTime.now())
                    .setLastHitTime(LocalDateTime.now())
                    .setTotalAmount(amountDecimal)
                    .setAvgAmount(amountDecimal);
            
            if (Boolean.TRUE.equals(isFirstReceive)) {
                summary.setIsFirstReceiveInRange(1)
                       .setFirstReceiveAmountInRange(amountDecimal)
                       .setFirstReceiveTimeInRange(LocalDateTime.now());
            }
            
            addressRangeSummaryMapper.insert(summary);
        } else {
            // 更新现有记录
            summary.setHitCount(summary.getHitCount() + 1)
                   .setLastHitTime(LocalDateTime.now())
                   .setTotalAmount(summary.getTotalAmount().add(amountDecimal));
            
            // 重新计算平均值
            summary.setAvgAmount(summary.getTotalAmount().divide(
                    new BigDecimal(summary.getHitCount()), 18, BigDecimal.ROUND_HALF_UP));
            
            // 如果是首次收款且之前没有记录
            if (Boolean.TRUE.equals(isFirstReceive) && summary.getIsFirstReceiveInRange() == 0) {
                summary.setIsFirstReceiveInRange(1)
                       .setFirstReceiveAmountInRange(amountDecimal)
                       .setFirstReceiveTimeInRange(LocalDateTime.now());
            }
            
            addressRangeSummaryMapper.updateById(summary);
        }
    }
}
