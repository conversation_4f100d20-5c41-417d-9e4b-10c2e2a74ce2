package com.client;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.blate.dto.OkhttpResp;
import com.blate.util.OkhttpUtils;
import com.dto.Transaction;
import com.entity.Address;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
public class OkxClient {

//post   https://www.oklink.com/api/explorer/v2/sol/transferTransList/u6PJ8DtQuPFnfmwHbGFULQ4u4EgjDiyYKjVEsynXq2w?t=*************
//get   https://www.oklink.com/api/explorer/v2/sol/transaction/FKabfd4PBVLXSNCKshLpPYrJP8B6ay8MzJbTpG3uKn4H?offset=0&limit=20&nonzeroValue=false&address=FKabfd4PBVLXSNCKshLpPYrJP8B6ay8MzJbTpG3uKn4H&chain=solana&t=*************
//get   https://www.oklink.com/api/explorer/v2/sol/transaction?offset=0&limit=20&nonzeroValue=false&address=u6PJ8DtQuPFnfmwHbGFULQ4u4EgjDiyYKjVEsynXq2w&chain=solana&t=*************
//get    https://www.oklink.com/api/explorer/v2/sol/account/384DBabLMyQuZrgiTTouxjmsZ6ZD9FkwDKsJwgp1ac7z?address=384DBabLMyQuZrgiTTouxjmsZ6ZD9FkwDKsJwgp1ac7z&chain=solana&t=*************

    private static final String baseUrl = "https://www.oklink.com/api/explorer/v2/sol";

    private static final String transferTransListUrl = "/transferTransList/";
    private static final String transactionUrl = "/transaction";
    private static final String accountUrl = "/account/384DBabLMyQuZrgiTTouxjmsZ6ZD9FkwDKsJwgp1ac7z?address=384DBabLMyQuZrgiTTouxjmsZ6ZD9FkwDKsJwgp1ac7z&chain=solana&t=*************";

    private static HashMap<String, String> header = new HashMap<>() {
        {
            put("x-apikey", "************************************************************************");
        }
    };

    private static long getNow() {
        return System.currentTimeMillis();
    }

    public static List<Address> getTransferTransList(String address) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("offset", "0");
        jsonObject.put("limit", "20");
        jsonObject.put("chain", "SOLANA");
        jsonObject.put("type", "0");
        jsonObject.put("address", address);
        jsonObject.put("action", "all");
        jsonObject.put("typeList", new Integer[]{0});

        OkhttpResp okhttpResp = OkhttpUtils.post(baseUrl + transferTransListUrl + "/" + address + "?t=" + getNow(), jsonObject, header);
        String jsonString = okhttpResp.getJsonString();
        JSONObject data = JSONObject.parseObject(jsonString).getJSONObject("data");
        JSONArray hits = data.getJSONArray("hits");
        return hits.toJavaList(Address.class);
    }

    public static Transaction getTransaction(String address) {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("offset", "0");
        parameters.put("limit", "20");
        parameters.put("nonzeroValue", "false");
        parameters.put("address", address);
        parameters.put("chain", "solana");
        parameters.put("t", getNow());

        OkhttpResp okhttpResp = OkhttpUtils.get(OkhttpUtils.buildUrl(baseUrl, transactionUrl + "/" + address, parameters), header);

        String jsonString = okhttpResp.getJsonString();
        JSONObject data = JSONObject.parseObject(jsonString).getJSONObject("data");

//         {
//        "total": 1218,
//        "hits": [
//            {
//                "status": "Success",
//                "signature": "4sgbxiFyey4Ceecx4oumWqxPAJ7wPgsdiWLRZGhorfvk7LkL4SrnntM7MzEayupszDs72Ts4mzxybfPjqenKQDQD",
//                "slot": 357051568,
//                "timestamp": 1754007738,
//                "programs": [
//                    "ComputeBudget111111111111111111111111111111",
//                    "11111111111111111111111111111111"
//                ],
//                "instructions": [],
//                "feePayer": "5Hr7wZg7oBpVhH5nngRqzr5W7ZFUfCsfEhbziZJak7fr",
//                "fee": 5.825E-6,
//                "solAmount": "0.000005845"
//            }]
        return data.toJavaObject(Transaction.class);
    }

    public static void main(String[] args) {
        String address = "u6PJ8DtQuPFnfmwHbGFULQ4u4EgjDiyYKjVEsynXq2w";


//        List<Address> transferTransList = getTransferTransList(address);
        Transaction transferTransList = getTransaction(address);

        System.out.println();


    }


}
