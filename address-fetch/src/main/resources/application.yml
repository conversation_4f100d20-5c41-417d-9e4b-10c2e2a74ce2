server:
  port: 9081

spring:
  application:
    name: address-fetch
  profiles:
    active: dev
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

logging:
  level:
    root: info
    com.mapper: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 如果需要数据库连接，取消下面的注释并配置
#spring:
#  datasource:
#    url: ***************************************************************************************************************************
#    username: root
#    password: 123456
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    hikari:
#      pool-name: AddressFetchHikariCP
#      minimum-idle: 5
#      idle-timeout: 180000
#      maximum-pool-size: 10
#      auto-commit: true
#      max-lifetime: 180000
#      connection-timeout: 30000
#      connection-test-query: SELECT 1

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    map-underscore-to-camel-case: true
#  mapper-locations: classpath:mapper/*.xml
