<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.AddressMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.entity.Address">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="amount_range_matched" property="amountRangeMatched" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, address, amount, amount_range_matched, create_time, update_time, creator
    </sql>

    <!-- 根据地址查询 -->
    <select id="selectByAddress" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM address
        WHERE address = #{address}
        ORDER BY create_time DESC
    </select>

    <!-- 根据金额区间查询地址列表 -->
    <select id="selectByAmountRange" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM address
        WHERE amount_range_matched = #{amountRange}
        ORDER BY create_time DESC
    </select>

    <!-- 根据多个金额区间查询地址列表 -->
    <select id="selectByAmountRanges" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM address
        WHERE amount_range_matched IN
        <foreach collection="amountRanges" item="range" open="(" separator="," close=")">
            #{range}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <!-- 根据金额范围查询 -->
    <select id="selectByAmountBetween" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM address
        WHERE amount BETWEEN #{minAmount} AND #{maxAmount}
        ORDER BY amount DESC, create_time DESC
    </select>

    <!-- 根据时间范围查询 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM address
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        <if test="amountRange != null and amountRange != ''">
            AND amount_range_matched = #{amountRange}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询地址列表 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM address
        <where>
            <if test="address != null and address != ''">
                AND address LIKE CONCAT('%', #{address}, '%')
            </if>
            <if test="amountRange != null and amountRange != ''">
                AND amount_range_matched = #{amountRange}
            </if>
            <if test="minAmount != null">
                AND amount >= #{minAmount}
            </if>
            <if test="maxAmount != null">
                AND amount <= #{maxAmount}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time <= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>

    <!-- 统计地址总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM address
        <where>
            <if test="address != null and address != ''">
                AND address LIKE CONCAT('%', #{address}, '%')
            </if>
            <if test="amountRange != null and amountRange != ''">
                AND amount_range_matched = #{amountRange}
            </if>
            <if test="minAmount != null">
                AND amount >= #{minAmount}
            </if>
            <if test="maxAmount != null">
                AND amount <= #{maxAmount}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time <= #{endTime}
            </if>
        </where>
    </select>

    <!-- 按金额区间统计地址数量 -->
    <select id="countByAmountRange" resultType="java.util.Map">
        SELECT 
            amount_range_matched as amountRange,
            COUNT(*) as addressCount,
            COUNT(DISTINCT address) as uniqueAddressCount,
            MIN(amount) as minAmount,
            MAX(amount) as maxAmount,
            AVG(amount) as avgAmount,
            SUM(amount) as totalAmount
        FROM address
        <where>
            <if test="amountRanges != null and amountRanges.size() > 0">
                AND amount_range_matched IN
                <foreach collection="amountRanges" item="range" open="(" separator="," close=")">
                    #{range}
                </foreach>
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time <= #{endTime}
            </if>
        </where>
        GROUP BY amount_range_matched
        ORDER BY addressCount DESC
    </select>

    <!-- 查询重复地址 -->
    <select id="selectDuplicateAddresses" resultType="java.util.Map">
        SELECT 
            address,
            COUNT(*) as count,
            GROUP_CONCAT(DISTINCT amount_range_matched) as ranges,
            MIN(create_time) as firstTime,
            MAX(create_time) as lastTime
        FROM address
        GROUP BY address
        HAVING COUNT(*) > 1
        ORDER BY count DESC, firstTime ASC
    </select>

    <!-- 批量插入地址 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO address (address, amount, amount_range_matched, create_time, update_time, creator)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.address}, #{item.amount}, #{item.amountRangeMatched}, 
             #{item.createTime}, #{item.updateTime}, #{item.creator})
        </foreach>
    </insert>

    <!-- 批量更新金额区间 -->
    <update id="batchUpdateAmountRange">
        UPDATE address 
        SET amount_range_matched = #{newAmountRange}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据地址删除 -->
    <delete id="deleteByAddress" parameterType="java.lang.String">
        DELETE FROM address WHERE address = #{address}
    </delete>

    <!-- 根据金额区间删除 -->
    <delete id="deleteByAmountRange" parameterType="java.lang.String">
        DELETE FROM address WHERE amount_range_matched = #{amountRange}
    </delete>

    <!-- 清理过期数据 -->
    <delete id="deleteExpiredData">
        DELETE FROM address 
        WHERE create_time &lt; #{expireTime}
        <if test="amountRange != null and amountRange != ''">
            AND amount_range_matched = #{amountRange}
        </if>
    </delete>

</mapper>
