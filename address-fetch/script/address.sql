DROP TABLE IF EXISTS smart_money;
CREATE TABLE address
(
    id                   BIGINT NOT NULL PRIMARY KEY,
    address              VARCHAR(100) COMMENT '钱包地址',
    amount               DECIMAL(30, 18) COMMENT '金额',
    amount_range_matched VARCHAR(100) COMMENT '命中的金额区间',

    create_time          TIMESTAMP COMMENT '创建时间',
    update_time          TIMESTAMP COMMENT '更新时间',
    creator              VARCHA<PERSON>(255) COMMENT '创建人'
);

-- 创建索引
CREATE INDEX idx_address_amount_range_matched ON address (amount_range_matched);