-- 地址监控主表
DROP TABLE IF EXISTS address_monitor;
CREATE TABLE address_monitor (
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    address VARCHAR(100) NOT NULL COMMENT '钱包地址',
    chain VARCHAR(50) NOT NULL COMMENT '区块链网络(ETH/BSC/POLYGON等)',
    
    -- 交易金额相关
    transaction_amount DECIMAL(30, 18) COMMENT '最新交易金额',
    amount_range_matched VARCHAR(50) COMMENT '命中的金额区间(如:4-5,8-9,23-45)',
    
    -- 首次收款标记
    is_first_receive TINYINT(1) DEFAULT 0 COMMENT '是否首次收款 0-否 1-是',
    first_receive_time TIMESTAMP NULL COMMENT '首次收款时间',
    first_receive_amount DECIMAL(30, 18) COMMENT '首次收款金额',
    
    -- 监控状态
    monitor_status TINYINT(1) DEFAULT 1 COMMENT '监控状态 0-停止 1-监控中',
    last_check_time TIMESTAMP NULL COMMENT '最后检查时间',
    check_count INT DEFAULT 0 COMMENT '检查次数',
    
    -- 统计信息
    total_transactions INT DEFAULT 0 COMMENT '总交易次数',
    total_amount DECIMAL(30, 18) DEFAULT 0 COMMENT '总交易金额',
    matched_count INT DEFAULT 0 COMMENT '命中区间次数',
    
    -- 系统字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(255) COMMENT '创建人',
    
    -- 索引
    UNIQUE KEY uk_address_chain (address, chain),
    INDEX idx_monitor_status (monitor_status),
    INDEX idx_first_receive (is_first_receive),
    INDEX idx_amount_range (amount_range_matched),
    INDEX idx_last_check_time (last_check_time),
    INDEX idx_create_time (create_time)
) COMMENT='地址监控主表';

-- 交易记录详情表
DROP TABLE IF EXISTS address_transaction_log;
CREATE TABLE address_transaction_log (
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    address VARCHAR(100) NOT NULL COMMENT '钱包地址',
    chain VARCHAR(50) NOT NULL COMMENT '区块链网络',
    
    -- 交易详情
    transaction_hash VARCHAR(100) COMMENT '交易哈希',
    transaction_amount DECIMAL(30, 18) NOT NULL COMMENT '交易金额',
    transaction_type TINYINT(1) COMMENT '交易类型 1-收款 2-转账',
    
    -- 区间匹配
    amount_range_matched VARCHAR(50) COMMENT '命中的金额区间',
    is_range_matched TINYINT(1) DEFAULT 0 COMMENT '是否命中区间 0-否 1-是',
    
    -- 首次收款判断
    is_first_receive TINYINT(1) DEFAULT 0 COMMENT '是否首次收款',
    api_check_result TEXT COMMENT 'API检查结果JSON',
    
    -- 处理状态
    process_status TINYINT(1) DEFAULT 0 COMMENT '处理状态 0-待处理 1-已处理 2-处理失败',
    process_time TIMESTAMP NULL COMMENT '处理时间',
    error_message TEXT COMMENT '错误信息',
    
    -- 系统字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    INDEX idx_address_chain (address, chain),
    INDEX idx_range_matched (is_range_matched),
    INDEX idx_first_receive (is_first_receive),
    INDEX idx_process_status (process_status),
    INDEX idx_transaction_hash (transaction_hash),
    INDEX idx_create_time (create_time)
) COMMENT='地址交易记录日志表';

-- 金额区间配置表
DROP TABLE IF EXISTS amount_range_config;
CREATE TABLE amount_range_config (
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    range_name VARCHAR(50) NOT NULL COMMENT '区间名称',
    min_amount DECIMAL(30, 18) NOT NULL COMMENT '最小金额',
    max_amount DECIMAL(30, 18) NOT NULL COMMENT '最大金额',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用 0-禁用 1-启用',
    priority INT DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
    
    -- 系统字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(255) COMMENT '创建人',
    
    -- 索引
    UNIQUE KEY uk_range_name (range_name),
    INDEX idx_active (is_active),
    INDEX idx_priority (priority),
    INDEX idx_amount_range (min_amount, max_amount)
) COMMENT='金额区间配置表';

-- 地址区间统计表 (用于快速查询和导出)
DROP TABLE IF EXISTS address_range_summary;
CREATE TABLE address_range_summary (
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    address VARCHAR(100) NOT NULL COMMENT '钱包地址',
    chain VARCHAR(50) NOT NULL COMMENT '区块链网络',
    range_name VARCHAR(50) NOT NULL COMMENT '金额区间名称',

    -- 统计信息
    hit_count INT DEFAULT 0 COMMENT '命中该区间次数',
    first_hit_time TIMESTAMP NULL COMMENT '首次命中时间',
    last_hit_time TIMESTAMP NULL COMMENT '最后命中时间',
    total_amount DECIMAL(30, 18) DEFAULT 0 COMMENT '该区间总金额',
    avg_amount DECIMAL(30, 18) DEFAULT 0 COMMENT '该区间平均金额',

    -- 首次收款相关
    is_first_receive_in_range TINYINT(1) DEFAULT 0 COMMENT '在该区间是否有首次收款',
    first_receive_amount_in_range DECIMAL(30, 18) COMMENT '该区间首次收款金额',
    first_receive_time_in_range TIMESTAMP NULL COMMENT '该区间首次收款时间',

    -- 导出标记
    export_status TINYINT(1) DEFAULT 0 COMMENT '导出状态 0-未导出 1-已导出',
    export_time TIMESTAMP NULL COMMENT '导出时间',
    export_batch_id VARCHAR(50) COMMENT '导出批次ID',

    -- 系统字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_address_chain_range (address, chain, range_name),
    INDEX idx_range_name (range_name),
    INDEX idx_hit_count (hit_count),
    INDEX idx_first_receive_in_range (is_first_receive_in_range),
    INDEX idx_export_status (export_status),
    INDEX idx_export_batch (export_batch_id),
    INDEX idx_first_hit_time (first_hit_time),
    INDEX idx_last_hit_time (last_hit_time)
) COMMENT='地址区间统计表';

-- 导出记录表
DROP TABLE IF EXISTS export_record;
CREATE TABLE export_record (
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    batch_id VARCHAR(50) NOT NULL COMMENT '导出批次ID',
    export_type TINYINT(1) NOT NULL COMMENT '导出类型 1-按区间 2-按时间 3-全量',

    -- 导出条件
    range_names TEXT COMMENT '导出的区间名称(JSON数组)',
    start_time TIMESTAMP NULL COMMENT '导出开始时间',
    end_time TIMESTAMP NULL COMMENT '导出结束时间',
    filter_conditions TEXT COMMENT '其他过滤条件(JSON)',

    -- 导出结果
    total_addresses INT DEFAULT 0 COMMENT '导出地址总数',
    file_path VARCHAR(500) COMMENT '导出文件路径',
    file_name VARCHAR(200) COMMENT '导出文件名',
    file_size BIGINT COMMENT '文件大小(字节)',

    -- 导出状态
    export_status TINYINT(1) DEFAULT 0 COMMENT '导出状态 0-进行中 1-成功 2-失败',
    error_message TEXT COMMENT '错误信息',

    -- 系统字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    complete_time TIMESTAMP NULL COMMENT '完成时间',
    creator VARCHAR(255) COMMENT '创建人',

    -- 索引
    UNIQUE KEY uk_batch_id (batch_id),
    INDEX idx_export_type (export_type),
    INDEX idx_export_status (export_status),
    INDEX idx_create_time (create_time),
    INDEX idx_creator (creator)
) COMMENT='导出记录表';

-- 插入默认的金额区间配置
INSERT INTO amount_range_config (range_name, min_amount, max_amount, priority) VALUES
('4-5', 4.0, 5.0, 1),
('8-9', 8.0, 9.0, 2),
('23-45', 23.0, 45.0, 3);

-- 创建一些有用的视图
-- 按区间统计地址数量的视图
CREATE VIEW v_address_range_stats AS
SELECT
    range_name,
    COUNT(DISTINCT address) as unique_addresses,
    COUNT(*) as total_records,
    SUM(hit_count) as total_hits,
    SUM(total_amount) as total_amount_sum,
    AVG(avg_amount) as avg_amount_overall,
    SUM(CASE WHEN is_first_receive_in_range = 1 THEN 1 ELSE 0 END) as first_receive_count,
    MIN(first_hit_time) as earliest_hit,
    MAX(last_hit_time) as latest_hit
FROM address_range_summary
GROUP BY range_name;

-- 地址详细信息视图(用于导出)
CREATE VIEW v_address_export_detail AS
SELECT
    ars.address,
    ars.chain,
    ars.range_name,
    ars.hit_count,
    ars.total_amount,
    ars.avg_amount,
    ars.first_hit_time,
    ars.last_hit_time,
    ars.is_first_receive_in_range,
    ars.first_receive_amount_in_range,
    ars.first_receive_time_in_range,
    am.monitor_status,
    am.total_transactions,
    am.matched_count,
    arc.min_amount as range_min,
    arc.max_amount as range_max
FROM address_range_summary ars
LEFT JOIN address_monitor am ON ars.address = am.address AND ars.chain = am.chain
LEFT JOIN amount_range_config arc ON ars.range_name = arc.range_name
WHERE arc.is_active = 1;
