package com.chatbot;

import com.blate.util.FileUtils;
import com.chatbot.client.DiscordBotClient;
import com.chatbot.client.TgBotClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication
@Slf4j
public class ChatBotApplication {
    public static void main(String[] args) {

        //针对本地代理不够彻底的情况使用
        System.setProperty("socksProxyHost", "127.0.0.1");
        System.setProperty("socksProxyPort", "7890");

        ConfigurableApplicationContext applicationContext = SpringApplication.run(ChatBotApplication.class, args);

        Auth.checkAuth();
        String tgBotTokenPath = FileUtils.getBasePath() + "tgBotToken.txt";
        log.info("检测tg token文件：{}", tgBotTokenPath);
        String tgBotToken = FileUtils.getTxt(tgBotTokenPath);
        log.info("读取tg token：{}", tgBotToken);

//        String discordBotTokenPath = FileUtils.getBasePath() + "discordBotToken.txt";
//        log.info("检测discord token文件：{}", discordBotTokenPath);
//        String discordBotToken = FileUtils.getTxt(discordBotTokenPath);
//        log.info("读取discord token：{}", discordBotToken);

        TgBotClient tgBotClient = applicationContext.getBean(TgBotClient.class);
        DiscordBotClient discordBotClient = applicationContext.getBean(DiscordBotClient.class);

//        Thread tgBotThread = new Thread(() -> tgBotClient.start(tgBotToken));
        Thread tgBotThread = new Thread(() -> tgBotClient.start());
        tgBotThread.start();

//        Thread discordBotThread = new Thread(() -> discordBotClient.start(discordBotToken));
//        discordBotThread.start();

    }


}
