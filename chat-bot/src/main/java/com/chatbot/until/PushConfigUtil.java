package com.chatbot.until;

import com.alibaba.fastjson.JSON;
import com.chatbot.dto.PushConfig;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

public class PushConfigUtil {
    // 将 PushConfig 对象保存到 JSON 文件
    public static void savePushConfig(String filePath, PushConfig pushConfig) {
        try {
            String jsonString = JSON.toJSONString(pushConfig, true); // 格式化 JSON
            Files.write(Paths.get(filePath), jsonString.getBytes());
            System.out.println("PushConfig 已保存到 " + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 从 JSON 文件中读取 PushConfig 对象
    public static PushConfig loadPushConfig(String filePath) {
        try {
            String jsonString = new String(Files.readAllBytes(Paths.get(filePath)));
            return JSON.parseObject(jsonString, PushConfig.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

}
