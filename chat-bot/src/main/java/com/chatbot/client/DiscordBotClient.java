package com.chatbot.client;


import com.alibaba.fastjson.JSONObject;
import com.blate.util.OkhttpUtils;
import com.chatbot.config.DiscordTotgConfig;
import com.chatbot.dto.DiscordTotg;
import com.chatbot.dto.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.javacord.api.DiscordApi;
import org.javacord.api.DiscordApiBuilder;
import org.javacord.api.entity.intent.Intent;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class DiscordBotClient {

    private static DiscordApi discordClient;

    private final TgBotClient tgBotClient;
    private final DiscordTotgConfig discordTotgConfig;

    public static void main(String[] args) {

        String url = "https://discord.com/api/webhooks/1327608503682072627/80izHVzWd-k3RTnG9ni-KcgVbGK4mUtvIf1BLuUhG6wP3BRvFNS16YXx17fAeDrteMZu";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content","\\uD83C\\uDD95\\uD83D\\uDFE2 BUY NPC on OKX DEX\\n\" +\n" +
                "                \"\\uD83D\\uDD39 测试\\n\" +\n" +
                "                \"\\n\" +\n" +
                "                \"\\uD83D\\uDD39**测试** swapped 0.01 [SOL](<https://solscan.io/token/So11111111111111111111111111111111111111112>) for 172,690.83 ($2.28) [NPC](<https://solscan.io/token/D2ojTo5SsWptEq2G4igHyUpEMmUXhAAPVAk61eEZbgxn>) @$0.0000131\\n\" +\n" +
                "                \"✊Holds: 172,690.83 NPC (0.001%)\\n\" +\n" +
                "                \"\\n\" +\n" +
                "                \"\\uD83D\\uDD17 #NPC | MC: $290.17K | Seen: 30+d: BE | DS | DT | PH | Bullx | \\uD83D\\uDC65INFO\\n\" +\n" +
                "                \"D2ojTo5SsWptEq2G4igHyUpEMmUXhAAPVAk61eEZbgxn\\n\" +\n" +
                "                \"\\n\" +\n" +
                "                \"TX | \\uD83E\\uDD16 RayBot\\n\" +\n" +
                "                \"\\n\" +\n" +
                "                \"NPC: ⭐ BullX NEO | MEVX | RAY\\n\" +\n" +
                "                \"NPC: BonkBot");
        OkhttpUtils.post(url,jsonObject);

    }


    public void start(String token) {
        if (ObjectUtils.isEmpty(token)) {
            log.info("discord Bot fail because no token");
            return;
        }
        discordClient = new DiscordApiBuilder().setToken(token).addIntents(Intent.MESSAGE_CONTENT).login().join();
        log.info("discord start success！");
        discordClient.addMessageCreateListener(event -> {
            long channelId = event.getChannel().getId();
            String name = event.getMessage().getAuthor().getName();
            org.javacord.api.entity.message.Message message = event.getMessage();
            String content = message.getContent();
            String richContent = StringUtils.EMPTY;
            if(ObjectUtils.isNotEmpty(message.getEmbeds())){
                richContent = message.getEmbeds().get(0).getDescription().get();
            }
            log.info("[discord] channelId:{} name:{}  message:{} webhookMessage:{}", channelId, name, content, richContent);
            DiscordTotg discordTotg = discordTotgConfig.getDiscordTotg();
            if (ObjectUtils.isEmpty(discordTotg)) {
                return;
            }
            Long channelIdConfig = discordTotg.getChannelId();
            Long chatIdConfig = discordTotg.getChatId();
            if (ObjectUtils.isEmpty(channelIdConfig) || ObjectUtils.isEmpty(chatIdConfig)) {
                return;
            }
            if (channelId == channelIdConfig) {//测试 1366768093
                if (ObjectUtils.isNotEmpty(richContent)) {
                    sendToTg(new Message(chatIdConfig, richContent));
                } else if (ObjectUtils.isNotEmpty(content)) {
                    sendToTg(new Message(chatIdConfig, content));
                }
            }

        });
    }

    public void sendToTg(Message message) {
        tgBotClient.sendMessage(message);
    }


}