package com.chatbot.client;


import com.blate.util.FileUtils;
import com.blate.util.IpUntil;
import com.chatbot.config.TgBotConfig;
import com.chatbot.dto.BscContract;
import com.chatbot.dto.BscMessage;
import com.chatbot.dto.Message;
import com.chatbot.dto.PushConfig;
import com.chatbot.until.PushConfigUtil;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.client.okhttp.OkHttpTelegramClient;
import org.telegram.telegrambots.longpolling.TelegramBotsLongPollingApplication;
import org.telegram.telegrambots.longpolling.util.LongPollingSingleThreadUpdateConsumer;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.meta.generics.TelegramClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
@NoArgsConstructor
@Accessors(chain = true)
public class TgBotClient implements LongPollingSingleThreadUpdateConsumer {

    private static final ConcurrentHashMap<String, BscContract> contractMap = new ConcurrentHashMap<>();//记录所有合约信息
    private static final Set<String> hasSendAddress = new HashSet<>();//记录发送过的合约信息
    private static final ConcurrentHashMap<String, Integer> wordHasSendCount = new ConcurrentHashMap<>();//关键词发送过的次数

    private static TelegramClient telegramClient;
    private ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static boolean isRunning = false;

    private static PushConfig pushConfig;

    private static final String OPEN = "开";
    private static final String CLOSE = "关";
    private static final String CLEAR = "清空关键词";
    private static final String PUSH_NUM_PRESS = "推送数量";
    private static final String HISTORY_EXPIRE_PRESS = "历史存活";

    private static final String pusConfigPath = FileUtils.getBasePath() + "pushConfig.txt";

    @Resource
    private TgBotConfig tgBotConfig;

    public TgBotClient(String token) {
        PushConfig initConfig = PushConfigUtil.loadPushConfig(pusConfigPath);
        pushConfig = ObjectUtils.isEmpty(initConfig) ? new PushConfig() : initConfig;
        log.info("初始配置参数：{}", pushConfig);
        telegramClient = new OkHttpTelegramClient(token);
        sendToMe("TgBot启动成功!");
    }

//    public void start(String token) {
    public void start() {
        String name = tgBotConfig.getName();
        String token = tgBotConfig.getToken();
        if (ObjectUtils.isEmpty(token)) {
            log.info("tg Bot fail because no token");
            return;
        }

        try (TelegramBotsLongPollingApplication botsApplication = new TelegramBotsLongPollingApplication()) {
            botsApplication.registerBot(token, new TgBotClient(token));
            log.info("tgBot start success！name:{} token:{}",name,token);
            log.info("http://localhost:9889 http://{}:9889",IpUntil.getIpLocal());
            Thread.currentThread().join();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void sendToMe(String text) {
        long blateChatId = 1366768093;
        SendMessage message = SendMessage.builder().chatId(blateChatId).text(text).build();
        try {
            telegramClient.execute(message);
        } catch (TelegramApiException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void consume(Update update) {
        if (update.hasMessage() && update.getMessage().hasText()) {
            String message = update.getMessage().getText();
            String name = update.getMessage().getFrom().getUserName();
            long chatId = update.getMessage().getChatId();
            String groupTitle = update.getMessage().getChat().getTitle();
            if (Objects.isNull(groupTitle)) {
                log.info("[tg person] chatId:{} name:{}  message:{}", chatId, name, message);
            } else {
                log.info("[tg group] chatId:{} name:{}  message:{}", chatId, name, message);

                if (OPEN.equals(message)) {
                    pushConfig.setOpenSend(true);
                } else if (CLOSE.equals(message)) {
                    pushConfig.setOpenSend(false);
                } else if (CLEAR.equals(message)) {
                    pushConfig.setWord(StringUtils.EMPTY);
                } else if (message.startsWith(PUSH_NUM_PRESS)) {
                    pushConfig.setPushNum(Integer.parseInt(message.replace(PUSH_NUM_PRESS, StringUtils.EMPTY)));
                } else if (message.startsWith(HISTORY_EXPIRE_PRESS)) {
                    pushConfig.setHistoryExpire(Integer.parseInt(message.replace(HISTORY_EXPIRE_PRESS, StringUtils.EMPTY)));
//                    monitor.setMaxTimeout(pushConfig.getHistoryExpire());
                } else {
                    pushConfig.setWord(message);
                    findHistory(chatId);
                }

                //将对象 PushConfig pushConfig序列化到文件
                PushConfigUtil.savePushConfig(pusConfigPath, pushConfig);
                //反馈配置结果
                Message reMsg = new Message(chatId, pushConfig.toString());
                sendMessage(reMsg);

            }

//            if (Objects.equals("blate", messageText) && Objects.equals("blate666", userName)) {
//            long blateChatId = 1366768093;
//                SendMessage message = SendMessage.builder().chatId(blateChatId).text(String.valueOf(chatId)).build();
//            SendMessage message = SendMessage.builder().chatId(chatId).text("你说的话：" + messageText).build();
//            try {
//                telegramClient.execute(message);
//            } catch (TelegramApiException e) {
//                e.printStackTrace();
//            }
//            }
        }
    }

    public void sendBscMessage(BscMessage message) {
        if (!pushConfig.isOpenSend()) {
            log.info("当前开关关闭，不发送");
            return;
        }
        BscContract bscContract = message.getBscContract();
        String address = bscContract.getAddress();
        if (contractMap.containsKey(address)) {
            log.info("合约{}已存在，不预处理", address);
            return;
        }
        contractMap.put(address, bscContract);
        Long chatId = message.getChatId();
        findHistory(chatId);
    }

    private void findHistory(Long chatId) {
        List<BscContract> allContract = getAllContract();
        if (ObjectUtils.isEmpty(allContract)) {
            log.info("历史合约为空，不发送");
            return;
        }
        BscContract bscContractLast = allContract.get(allContract.size() - 1);
        String word = pushConfig.getWord();
        String formatWord = format(pushConfig.getWord());
        int pushNum = pushConfig.getPushNum();

        if (ObjectUtils.isEmpty(word)) {
            String address = bscContractLast.getAddress();
            if (checkIsSend(bscContractLast)) {
                log.info("发送过的合约，不予发送 {}", address);
                return;
            }
            String name1 = bscContractLast.getName1();
            String name2 = bscContractLast.getName2();
            Integer hasSendCount1 = wordHasSendCount.getOrDefault(name1, 0);
            Integer hasSendCount2 = wordHasSendCount.getOrDefault(name2, 0);
            if (hasSendCount1 >= pushNum || hasSendCount2 >= pushNum) {
                log.info("超过关键词发送次数，不予发送 {}", address);
                return;
            }

            log.info("开始发送：{}", address);
            sendMessage(new Message(chatId, bscContractLast.createMessage()));
            hasSendAddress.add(address);
            wordHasSendCount.put(name1, hasSendCount1 + 1);
            wordHasSendCount.put(name2, hasSendCount2 + 1);
            return;
        }
        // 筛选符合条件的合约
        List<BscContract> filteredContracts = allContract.stream()
                .filter(contract -> StringUtils.isEmpty(word) || format(contract.getName1()).equals(formatWord) || format(contract.getName2()).equals(formatWord)) // 关键词匹配
                .filter(contract -> contract.getSendCount() == 0)
                .sorted(Comparator.comparingLong(BscContract::getId)) // 按 id 从小到大排序
                .limit(pushNum) // 取前 pushNum 个
                .toList();

        if (ObjectUtils.isEmpty(filteredContracts)) {
            log.info("未找到关键词为{}的合约，不发送", word);
            return;
        }
        // 输出合约信息
        for (BscContract contract : filteredContracts) {
            if (checkIsSend(contract)) {
                log.info("发送过的合约，不予发送");
                continue;
            }
            Integer hasSendCount = wordHasSendCount.getOrDefault(word, 0);
            if (hasSendCount >= pushNum) {
                log.info("超过关键词发送次数，不予发送");
                return;
            }

            log.info("开始发送：{}", contract.getAddress());
            sendMessage(new Message(chatId, contract.createMessage()));
            hasSendAddress.add(contract.getAddress());
            wordHasSendCount.put(word, hasSendCount + 1);
        }
    }

    private boolean checkIsSend(BscContract bscContract) {
        return hasSendAddress.contains(bscContract.getAddress());
    }

    private static List<BscContract> getAllContract() {
        long now = System.currentTimeMillis() / 1000;
        List<BscContract> list = contractMap.values().stream().toList();
        List<BscContract> res = list.stream()
                .sorted(Comparator.comparingLong(BscContract::getId)) // 按 id 从小到大排序
                .filter(contract -> ObjectUtils.isNotEmpty(contract.getOt()) && (now - contract.getOt()) <= pushConfig.getHistoryExpire())
                .toList();
        return res;
    }

    private static String format(String text) {//去除所有空格并且全部转为小写
        return text.replaceAll("\\s+", "").toLowerCase();
    }

    public void startSend(Message message) {
        Long rate = message.getRate();
        if (rate <= 0) {
            throw new IllegalArgumentException("频率必须大于0");
        }
        if (isRunning) {
            System.out.println("任务已经在运行中！");
            return;
        }
        isRunning = true;
        Long num = message.getNum();
        AtomicLong initNum = new AtomicLong();
        Runnable sendTask = () -> {
            sendMessage(message);
            initNum.getAndIncrement();
            log.info("消息已发送({}/{}): {}", initNum.get(), num, message);
            if (initNum.get() == num) {
                log.info("消息达到上限停止");
                stopSend();
            }
        };
        scheduler.scheduleWithFixedDelay(sendTask, 0, rate, TimeUnit.SECONDS);
    }


    public void stopSend() {
        if (isRunning) {
            scheduler.shutdown();
            isRunning = false;
            System.out.println("任务已停止");
            scheduler = Executors.newSingleThreadScheduledExecutor();
        } else {
            System.out.println("没有正在运行的任务");
        }
    }

    public void sendMessage(Message message) {
//        SendMessage sendMessage = SendMessage.builder().chatId(message.getChatId()).text(message.getText()).build();
        SendMessage sendMessage = SendMessage.builder().parseMode("MarkDown").chatId(message.getChatId()).text(message.getText()).build();
        try {
            telegramClient.execute(sendMessage);
            log.info("tg发送完毕");
        } catch (TelegramApiException e) {
            e.printStackTrace();
        }
    }

}