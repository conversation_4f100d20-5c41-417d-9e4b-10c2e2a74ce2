package com.chatbot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PushConfig implements Serializable {
    private boolean isOpenSend = true;//推送开关
    private String word = StringUtils.EMPTY;//过滤关键词 模糊查询
    private int pushNum = 3;//推送次数
    private long historyExpire = 5L;//历史存活时间 默认30秒

    private Map<String, Integer> existMap = new HashMap<>();

    @Override
    public String toString() {
        String help = "-----\n命令帮助\n1.`开`\n2.`关`\n3.`清空关键词`\n4.`推送数量`X\n5.`历史存活`X";
        return String.format("开关状态：%s\n当前name关键词：%s\n推送数量%s\n历史存活%s\n%s", isOpenSend ? "开" : "关", word, pushNum, historyExpire, help);
    }

}