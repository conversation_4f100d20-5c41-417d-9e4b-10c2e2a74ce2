package com.chatbot.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

// 合约类
@Data
@NoArgsConstructor
public class BscContract implements Serializable {
    private Long id;
    private Long ot;
    private String name1;
    private String name2;
    private String address;
    private int sendCount = 0;

    public BscContract(Long id, Long ot, String name1, String name2, String address) {
        this.id = id;
        this.ot = ot;
        this.name1 = name1;
        this.name2 = name2;
        this.address = address;
    }

//    @Override
//    public String toString() {
//
//        String now = DateUtil.now();
//        return String.format("`%s`\n`%s`\nname1：`%s`\nname2：`%s`\n合约：`%s`",
//                now, DateUtil.format(DateUtil.date(ot * 1000), "yyyy-MM-dd HH:mm:ss"), name1, name2, address);
//    }

    public String createMessage() {
        String now = DateUtil.now();
        return String.format("`%s`\n`%s`\nname1：`%s`\nname2：`%s`\n合约：`%s`",
                now, DateUtil.format(DateUtil.date(ot * 1000), "yyyy-MM-dd HH:mm:ss"), name1, name2, address);
    }

    public static void main(String[] args) {
        long ot = 1743484038L;
        // 格式化输出
        String formattedTime = DateUtil.format(DateUtil.date(ot * 1000), "yyyy-MM-dd HH:mm:ss");
        System.out.println(formattedTime);
    }

}
