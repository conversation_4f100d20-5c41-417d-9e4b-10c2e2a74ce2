package com.chatbot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class BscContractMonitor {
    private long maxTimeout = 60;
    private ConcurrentHashMap<String, BscContract> contractMap = new ConcurrentHashMap<>();

    // 添加合约
    public void addContract(BscContract contract) {
        contractMap.put(contract.getAddress(), contract);
    }

    // 获取合约对象（存在则返回对象，不存在返回 null）
    public BscContract getContract(String contractAddress) {
        return contractMap.get(contractAddress);
    }

    public List<BscContract> getAllContract() {
        long now = System.currentTimeMillis()/1000;
        List<BscContract> list = contractMap.values().stream().toList();
        log.info(String.valueOf(list.size()));
        List<BscContract> res = list.stream().filter(contract -> ObjectUtils.isNotEmpty(contract.getOt()) && (now - contract.getOt()) <= maxTimeout).toList();
        return res;
    }

    public static void main(String[] args) throws InterruptedException {
        BscContractMonitor monitor = new BscContractMonitor();
        monitor.setMaxTimeout(5);

        // 模拟新合约
        BscContract contract1 = new BscContract(System.currentTimeMillis(), System.currentTimeMillis(), "Name1", "Name2", "0x123");
        BscContract contract2 = new BscContract(System.currentTimeMillis(), System.currentTimeMillis(), "Test1", "Test2", "0x456");

        // 添加合约
        monitor.addContract(contract1);
        monitor.addContract(contract2);

        // 监控检测（每秒检查一次）
        for (int i = 0; i < 70; i++) {
            String addr = "0x123";
            BscContract contract = monitor.getContract(addr);
            List<BscContract> allContract = monitor.getAllContract();
            allContract.forEach(System.out::println);
            System.out.println();
            Thread.sleep(1000);
        }
    }

}

