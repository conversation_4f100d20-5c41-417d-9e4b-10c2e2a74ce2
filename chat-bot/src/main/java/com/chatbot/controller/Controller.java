package com.chatbot.controller;


import com.blate.reponse.Resp;
import com.chatbot.client.TgBotClient;
import com.chatbot.config.DiscordTotgConfig;
import com.chatbot.dto.BscMessage;
import com.chatbot.dto.DiscordTotg;
import com.chatbot.dto.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping()
@Slf4j
@RequiredArgsConstructor
public class Controller {
    private final TgBotClient tgBotClient;
    private final DiscordTotgConfig discordTotgConfig;

    @PostMapping("/startSend")
    public Resp startSend(@RequestBody Message message) {
        log.info("获取消息 {}", message);
        tgBotClient.startSend(message);
        return Resp.success(message, "发送成功请检查");
    }

//    @PostMapping("/sendOneMessage")
//    public Resp sendOneMessage(@RequestBody Message message) {
//        log.info("获取消息 {}", message);
//        tgBotClient.sendOneMessage(message);
//        return Resp.success(message, "发送成功请检查");
//    }

    private static final ConcurrentHashMap<String, Boolean> processedAddresses = new ConcurrentHashMap<>();

    @PostMapping("/sendBscMessage")
    public Resp sendOneMessage(@RequestBody BscMessage message) {
        log.info("获取bsc消息 {}", message);
        String address = message.getBscContract().getAddress();
        // 如果已经处理过该 address，则直接忽略
        if (processedAddresses.putIfAbsent(address, true) != null) {
            log.info("该 address 已经处理过，忽略: {}", address);
            return Resp.success(message, "重复请求，已忽略");
        }
        tgBotClient.sendBscMessage(message);
        return Resp.success(message, "发送成功请检查");
    }

    @GetMapping("/stopSend")
    public Resp stopSend() {
        tgBotClient.stopSend();
        return Resp.success("停止成功！");
    }


    @PostMapping("/updateDiscordTotgConfig")
    public Resp updateDiscordTotgConfig(@RequestBody DiscordTotg discordTotg) {
        log.info("获取配置 {}", discordTotg);
        discordTotgConfig.setDiscordTotg(discordTotg);
        return Resp.success(discordTotgConfig.getDiscordTotg(), "配置成功");
    }

}
