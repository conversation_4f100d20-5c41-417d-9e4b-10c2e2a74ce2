<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>tgBot</title>
    <script>
        function startSend() {
            const chatId = document.getElementById('chatId').value;
            const text = document.getElementById('text').value;
            const rate = document.getElementById('rate').value;
            const num = document.getElementById('num').value;
            const data = {
                chatId: chatId,
                text: text,
                rate: rate,
                num: num,
            };

            fetch('/startSend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('response').innerText = JSON.stringify(data, null, 2);
                })
                .catch(error => console.error('Error:', error));
        }

        function clearResponse() {
            document.getElementById('response').innerText = "";  // 清空响应区域
        }

        function stopSend() {
            fetch('/stopSend', {
                method: 'GET',
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('response').innerText = JSON.stringify(data, null, 2);
                })
                .catch(error => console.error('Error:', error));
        }

        function updateConfig() {
            const channelId = document.getElementById('channelId').value;
            const chatId = document.getElementById('chatId2').value;
            const data = {
                channelId: channelId,
                chatId: chatId,
            };
            fetch('/updateDiscordTotgConfig', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('response').innerText = JSON.stringify(data, null, 2);
                })
                .catch(error => console.error('Error:', error));
        }

    </script>
</head>
<body>
<h1>Blate Bot</h1>
<h2>Tg Bot配置</h2>
<div>
    <label for="chatId">发送id：</label>
    <input type="text" id="chatId" placeholder="请输入chatId" style="width: 300px">
</div>
<div>
    <label for="text">发送内容：</label>
    <input type="text" id="text" placeholder="请输入发送内容" style="width: 300px">
</div>
<div>
    <label for="rate">发送频率：</label>
    <input type="text" id="rate" placeholder="请输入发送频率（几秒一条）" style="width: 300px">
</div>
<div>
    <label for="num">发送次数上限：</label>
    <input type="text" id="num" placeholder="请输入发送次数上限" style="width: 300px">
</div>
<div>
    <button onclick="startSend()">开始发送</button>
    <button onclick="stopSend()">停止发送</button>
    <button onclick="clearResponse()">清空响应</button>
</div>
<h2>discord 转发 tg Bot配置</h2>
<div>
    <label for="channelId">从discord哪里来：</label>
    <input type="text" id="channelId" placeholder="请输入 discord channelId" style="width: 300px">
</div>
<div>
    <label for="chatId2">去tg哪里：</label>
    <input type="text" id="chatId2" placeholder="请输入 tg chatId" style="width: 300px">
</div>
<div>
    <button onclick="updateConfig()">保存配置立即生效</button>
</div>
<div>
    <h3>响应：</h3>
    <p id="response"></p>
</div>
</body>
</html>
