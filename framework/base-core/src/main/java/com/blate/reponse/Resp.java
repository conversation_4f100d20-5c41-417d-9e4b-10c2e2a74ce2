package com.blate.reponse;

import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;


@Data
public class Resp<T> {

    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private String code;
    private String message;
    private T data;
    private String time;


    public static Resp success() {
        Resp resp01 = new Resp();
        resp01.setCode(RespCode.SUCCESS.getCode());
        resp01.setMessage(RespCode.SUCCESS.getMessage());
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static Resp fail() {
        Resp resp01 = new Resp();
        resp01.setCode(RespCode.FAIL.getCode());
        resp01.setMessage(RespCode.FAIL.getMessage());
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static Resp fail(String e) {
        Resp resp01 = new Resp();
        resp01.setCode(RespCode.FAIL.getCode());
        resp01.setMessage(e);
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static Resp fail(String code, String e) {
        Resp resp01 = new Resp();
        resp01.setCode(code);
        resp01.setMessage(e);
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static Resp success(String message) {
        Resp resp01 = new Resp();
        resp01.setCode(RespCode.SUCCESS.getCode());
        resp01.setMessage(message);
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static <T> Resp success(T date) {
        Resp resp01 = new Resp();
        resp01.setCode(RespCode.SUCCESS.getCode());
        resp01.setData(date);
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static <T> Resp success(Object date, String message) {
        Resp resp01 = new Resp();
        resp01.setCode(RespCode.SUCCESS.getCode());
        resp01.setData(date);
        resp01.setMessage(message);
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static Resp fail(RespCode respCode, String message) {
        Resp resp01 = new Resp();
        resp01.setCode(respCode.getCode());
        resp01.setMessage(message);
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static Resp failWithData(String message, Object data) {
        Resp resp01 = new Resp();
        resp01.setCode(RespCode.FAIL.getCode());
        resp01.setMessage(message);
        resp01.setData(data);
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public static Resp fail(RespCode respCode) {
        Resp resp01 = new Resp();
        resp01.setCode(respCode.getCode());
        resp01.setMessage(respCode.getMessage());
        resp01.setTime(SIMPLE_DATE_FORMAT.format(new Date()));
        return resp01;
    }

    public boolean isSuccess() {
        return RespCode.SUCCESS.getCode().equals(this.code);
    }

}
