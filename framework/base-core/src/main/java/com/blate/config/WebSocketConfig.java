package com.blate.config;


import com.blate.websocket.WsServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * 子工程继承这个类重写配置路径即可开启服务端websocket服务
 *
 * <AUTHOR>
 * @since 2025/1/7 13:26:06
 */
@Configuration
@EnableWebSocket
@Slf4j
public class WebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new WsServer(), "/chat");
        log.info("默认WS服务端启动成功！");
    }

}