package com.blate.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.*;

@Slf4j
public class WsServer implements WebSocketHandler {

    private static final Map<String, WebSocketSession> CLIENT_MAP = new HashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        CLIENT_MAP.put(session.getId(), session);
        String clientIp = session.getRemoteAddress().getAddress().getHostAddress();
        log.info("建立连接({}):{}", clientIp, session);
        log.info("当前在线数：{}", CLIENT_MAP.size());
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) {
        log.info("处理消息:{}", message);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.info("错误处理");
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        String clientIp = session.getRemoteAddress().getAddress().getHostAddress();
        log.info("连接断开({}):{} {}", clientIp, session, closeStatus);
        CLIENT_MAP.remove(session.getId());
        log.info("当前在线数：{}", CLIENT_MAP.size());
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}