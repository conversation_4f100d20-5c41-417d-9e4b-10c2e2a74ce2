package com.blate.util;


import com.blate.dto.ProxyInfo;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

public class ProxyUtil {

//    @Retryable(value = {Exception.class}, maxAttempts = 20, backoff = @Backoff(delay = 1000, multiplier = 2))
    public static ProxyInfo getProxy() {
        String url = "http://get.syhttp.com/sygetip?apikey=556d8f33&pwd=d829bc131b329cd6cbfe60861586396b&getnum=1&geshi=1&fenge=1";
        String jsonString = OkhttpUtils.get(url).getJsonString();
        return new ProxyInfo( jsonString.split(":")[0], Integer.parseInt(jsonString.split(":")[1]));
    }

    public static void main(String[] args) {

        System.out.println(getProxy());

    }

}