package com.blate.util;

import com.alibaba.fastjson.JSONObject;
import com.blate.dto.OkhttpResp;
import com.blate.dto.ProxyInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class OkhttpUtils {

//    public static final OkHttpClient OK_HTTP_CLIENT_SHORT = new OkHttpClient.Builder()
//            .connectTimeout(15, TimeUnit.SECONDS)
//            .readTimeout(15, TimeUnit.SECONDS)
//            .build();

//    public static final OkHttpClient OK_HTTP_CLIENT = new OkHttpClient.Builder()
//            .connectTimeout(30, TimeUnit.SECONDS)
//            .readTimeout(30, TimeUnit.SECONDS)
//            .build();

//    public static final OkHttpClient OK_HTTP_CLIENT_LONG = new OkHttpClient.Builder()
//            .connectTimeout(60, TimeUnit.SECONDS)
//            .readTimeout(60, TimeUnit.SECONDS)
//            .build();

    private static final MediaType REQUEST_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    public static final String ZUO_XIE_GANG = "/";
    public static final String WEN_HAO = "?";
    public static final String QU_ZHI = "&";
    public static final String DENG_YU = "=";
    private static OkHttpClient OK_HTTP_CLIENT_PROXY;

    private static OkHttpClient getOkhttpClient(ProxyInfo proxyInfo) {
        if (!ObjectUtils.isEmpty(OK_HTTP_CLIENT_PROXY)) {
            return OK_HTTP_CLIENT_PROXY;
        }
        synchronized (OkhttpUtils.class) {
            if (ObjectUtils.isEmpty(OK_HTTP_CLIENT_PROXY)) {
                OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder()
                        .connectTimeout(30, TimeUnit.SECONDS)
                        .readTimeout(30, TimeUnit.SECONDS);
                if (!ObjectUtils.isEmpty(proxyInfo) && !ObjectUtils.isEmpty(proxyInfo.getHost()) && !ObjectUtils.isEmpty(proxyInfo.getPort())) {
                    clientBuilder.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyInfo.getHost(), proxyInfo.getPort())));
                }
                OK_HTTP_CLIENT_PROXY = clientBuilder.build();
            }
        }
        return OK_HTTP_CLIENT_PROXY;
    }

    public static OkhttpResp get(String url) {
        return get(url, null);
    }

    public static OkhttpResp get(String url, Map<String, String> headers) {
        return get(url, headers, null);
    }

    public static OkhttpResp post(String url, JSONObject jsonBody) {
        return post(url, jsonBody, null);
    }

    public static OkhttpResp post(String url, JSONObject jsonBody, Map<String, String> headers) {
        return post(url, jsonBody, headers, null);
    }

    public static OkhttpResp get(String url, Map<String, String> headers, ProxyInfo proxyInfo) {
        OkHttpClient okhttpClient = getOkhttpClient(proxyInfo);
        Request.Builder request = new Request.Builder().url(url).get();
        addHeader(request, headers);
        OkhttpResp resp = null;
        try {
            Call call = okhttpClient.newCall(request.build());
            Response response = call.execute();
            resp = new OkhttpResp(response.code(), response.message(), response.body().string());
            if (response.code() == 200) {
                log.debug("get req success --> {}", resp);
                return resp;
            } else {
                log.debug("get req fail --> {}", resp);
            }
        } catch (Exception e) {
            log.error("get req exception  ", e);
        }
        return resp;
    }

    public static OkhttpResp post(String url, JSONObject jsonBody, Map<String, String> headers, ProxyInfo proxyInfo) {
        OkHttpClient okhttpClient = getOkhttpClient(proxyInfo);
        RequestBody requestBody = RequestBody.create(REQUEST_MEDIA_TYPE, JSONObject.toJSONString(jsonBody));
        Request.Builder request = new Request.Builder().url(url).post(requestBody);
        addHeader(request, headers);
        OkhttpResp resp = null;
        try {
            Call call = okhttpClient.newCall(request.build());
            Response response = call.execute();
            resp = new OkhttpResp(response.code(), response.message(), response.body().string());
            if (response.code() == 200) {
                log.debug("post req success --> {}", resp);
                return resp;
            } else {
                log.debug("post req fail --> {}", resp);
            }
        } catch (Exception e) {
            log.error("post req exception ", e);
        }
        return resp;
    }

    private static void addHeader(Request.Builder request, Map<String, String> headers) {
        if (headers != null) {
            headers.forEach(request::addHeader);
        }
    }

    public static String buildUrl(String baseUrl, String urlPath, LinkedHashMap<String, Object> parameters) {
        StringBuilder sb = new StringBuilder();

        if (!baseUrl.endsWith(ZUO_XIE_GANG) && !urlPath.startsWith(ZUO_XIE_GANG)) {
            sb.append(baseUrl).append(ZUO_XIE_GANG).append(urlPath);
        } else if (baseUrl.endsWith(ZUO_XIE_GANG) && urlPath.startsWith(ZUO_XIE_GANG)) {
            sb.append(baseUrl, 0, baseUrl.length() - 1).append(urlPath);
        } else {
            sb.append(baseUrl).append(urlPath);
        }

        if (parameters != null && !parameters.isEmpty()) {
            sb.append(WEN_HAO);
            sb.append(joinQueryParameters(parameters));
        }

        return sb.toString();
    }

    private static String joinQueryParameters(LinkedHashMap<String, Object> parameters) {
        StringBuilder query = new StringBuilder();

        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String key = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8);
            String value = URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8);
            if (query.length() > 0) {
                query.append(QU_ZHI);
            }
            query.append(key).append(DENG_YU).append(value);
        }

        return query.toString();
    }

}