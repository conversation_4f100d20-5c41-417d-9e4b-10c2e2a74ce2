package com.blate.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

//TimeCuoToTime 传long或者string无论10位还是13位         type=1返回秒级时间，type=2返回毫秒级时间
//TimeToTimeCuo 传秒级时间或者毫秒级时间            type=1返回秒级时间戳（13位），type=2返回毫秒级时间戳（13位）
public class TimeUtils {

    public static <T> String TimeCuoToTime(T time, int type) {
        String timeStr = time instanceof Long ? String.valueOf(time) : time.toString();
        if (timeStr.length() == 10) timeStr += "000";
        SimpleDateFormat sdf = type == 1 ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss") : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SS");
        return sdf.format(new Date(Long.parseLong(timeStr)));
    }

    public static long TimeToTimeCuo(String time, int type) {
        if (time.length() == 19) time += ":00";
        SimpleDateFormat sdf = type == 1 ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss") : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SS");
        long timeLongBys = 0;
        try {
            timeLongBys = sdf.parse(time).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeLongBys;
    }

    public static long getNowTimeCuo(int type) {
        return type == 1 ? System.currentTimeMillis() / 1000 : System.currentTimeMillis();
    }

    public static String getNowTime(int type) {
        return type == 1 ? LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SS"));
    }

    public static String getNowTimeLowLine(int type) {
        return type == 1 ? LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH_mm_ss")) : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH_mm_ss_SS"));
    }

    public static String isoToLocalTime(String isoTime, int type) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));  //获取时区

        long timeCuo = 0;
        try {
            timeCuo = sdf.parse(isoTime).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return TimeCuoToTime(timeCuo, type);
    }

    public static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
