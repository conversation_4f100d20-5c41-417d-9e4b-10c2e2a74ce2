package com.blate.util;

import com.alibaba.excel.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Slf4j
@AllArgsConstructor
public class FileUtils {


    public static String getJarBasePath() {
        ApplicationHome ah = new ApplicationHome(FileUtils.class);
        File file = ah.getSource();
        String path = file.getParentFile().toString();
        if (path.contains("target")) {
            path = path.replace("target", "");
        } else {
            path = path + File.separator;
        }
        return path;
    }

    public static String getBasePath() {
        return System.getProperty("user.dir")+ FileSystems.getDefault().getSeparator();
    }

    public static List<String> getTxtAndSplit(String path) {
        try {
            String data = new String(Files.readAllBytes(Paths.get(path)));
            return Arrays.asList(data.split(System.lineSeparator()));
        } catch (IOException e) {
            System.err.println("Error reading file at path: " + path);
            return Collections.emptyList();
        }
    }

    public static String getTxt(String path, int line) {
        try {
            String data = new String(Files.readAllBytes(Paths.get(path)));
            List<String> list = Arrays.asList(data.split(System.lineSeparator()));//// TODO: 2024/12/27  wondwos平台兼容问题待处理 
            return list.get(line);
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

    public static String getTxt(String path) {
        String data = null;
        try {
            data = new String(Files.readAllBytes(Paths.get(path)));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return data;
    }

    public static <T> void writeTextOver(String path, List<T> obList) {
        try (BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(path))) {
            if (obList == null || obList.isEmpty()) {
                return;
            }
            for (T obj : obList) {
                String line = obj.toString();//自己实现toString
                bufferedWriter.write(line);
                bufferedWriter.write("\n");
            }
        } catch (IOException e) {
            System.err.println("文件异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
