-- 数据库 bot  admin  jianghu666.

# 策略结果表
drop table if exists strategy_result;
create table strategy_result
(
    id                      bigint         not null comment '策略结果id'
        primary key,
    ip                      varchar(45)    null comment 'ip地址',
    symbol                  varchar(255)   null comment '币种',
    `interval`              varchar(255)   null comment '周期',
    kline_window            int            null comment '策略窗口',
    take_profit             decimal(20, 2) null comment '止盈百分点',
    stop_loss_profit        decimal(20, 2) null comment '止损百分点',
    trade_mode              varchar(255)   null comment '交易模式',
    kline_strategy          varchar(255)   null comment '交易策略',
    account_initial_balance decimal(20, 2) null comment '账户初始余额',
    position                decimal(20, 2) null comment '账户单次交易仓位',
    start_time              datetime       null comment '策略开始时间',
    end_time                datetime       null comment '策略结束时间',
    end_balance             decimal(20, 8) null comment '最终余额',
    yield                   decimal(20, 8) null comment '收益率',
    create_time             datetime       null comment '创建时间',
    update_time             datetime       null comment '更新时间',
    creator                 varchar(255)   null
)



