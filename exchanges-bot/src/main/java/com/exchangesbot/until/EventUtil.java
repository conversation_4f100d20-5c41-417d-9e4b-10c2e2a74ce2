package com.exchangesbot.until;


import cn.hutool.core.date.DateUtil;
import com.exchangesbot.dto.Kline;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
public class EventUtil {

    public static void logCurrentKline(List<Kline> klineList, String message) {
        Kline currentKline = klineList.get(klineList.size() - 1);
        Long openTime = currentKline.getOpenTime();
        BigDecimal closePrice = currentKline.getClosePrice();
        BigDecimal highPrice = currentKline.getHighPrice();
        BigDecimal lowPrice = currentKline.getLowPrice();
        Boolean isHighBollMiddle = currentKline.getIsHighBollMiddle();
        log.info("【{}】观察到k线数量({}) {} 开盘时间：{} 收盘价：{} 最高价：{} 最低价：{}", message, klineList.size(), isHighBollMiddle, DateUtil.date(openTime), closePrice, highPrice, lowPrice);
    }

}