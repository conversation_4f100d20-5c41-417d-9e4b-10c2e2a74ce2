package com.exchangesbot.until;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.exchangesbot.constant.CommonConstant;
import com.exchangesbot.dto.Kline;
import com.exchangesbot.dto.TimeRange;
import com.exchangesbot.constant.enums.IntervalEnum;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class KlineCalculateUtil {

    /**
     * 根据k线周期和理论开始时间计算出实际开始时间
     */
    public static long getShouldStartTime(String startTime, IntervalEnum interval, int periods) {
        DateTime parse = DateUtil.parse(startTime);
        periods--;
        long res = 0;
        switch (interval) {
            case ONE_SECOND -> res = DateUtil.offsetSecond(parse, -periods).getTime();
            case ONE_MINUTE -> res = DateUtil.offsetMinute(parse, -periods).getTime();
            case THREE_MINUTES -> res = DateUtil.offsetMinute(parse, -periods * 3).getTime();
            case FIVE_MINUTES -> res = DateUtil.offsetMinute(parse, -periods * 5).getTime();
            case FIFTEEN_MINUTES -> res = DateUtil.offsetMinute(parse, -periods * 15).getTime();
            case THIRTY_MINUTES -> res = DateUtil.offsetMinute(parse, -periods * 30).getTime();
            case ONE_HOUR -> res = DateUtil.offsetHour(parse, -periods).getTime();
            case TWO_HOURS -> res = DateUtil.offsetHour(parse, -periods * 2).getTime();
            case FOUR_HOURS -> res = DateUtil.offsetHour(parse, -periods * 4).getTime();
            case SIX_HOURS -> res = DateUtil.offsetHour(parse, -periods * 6).getTime();
            case EIGHT_HOURS -> res = DateUtil.offsetHour(parse, -periods * 8).getTime();
            case TWELVE_HOURS -> res = DateUtil.offsetHour(parse, -periods * 12).getTime();
            case ONE_DAY -> res = DateUtil.offsetDay(parse, -periods).getTime();
            case THREE_DAYS -> res = DateUtil.offsetDay(parse, -periods * 3).getTime();
            case ONE_WEEK -> res = DateUtil.offsetDay(parse, -periods * 7).getTime();
            case ONE_MONTH -> res = DateUtil.offsetDay(parse, -periods * 30).getTime();
        }
        return res;
    }

    /**
     * 拆分时间区间,每部分最多包含 maxKlineNum 条K线(为了适配第三方)
     */
    public static List<TimeRange> calculateTimeRanges(long startTime, long endTime, IntervalEnum interval, int maxKlineNum) {
        List<TimeRange> timeRanges = new ArrayList<>();
        long totalKlineNum = KlineCalculateUtil.calculateKlineNum(startTime, endTime, interval);
        log.info("本次理论获取{}条K线", totalKlineNum);
        int partCount = (int) Math.ceil((double) totalKlineNum / maxKlineNum);
        for (int i = 0; i < partCount; i++) {
            long intervalMillis = IntervalEnum.INTERVAL_MILLIS_MAP.get(interval);
            long partStartTime = startTime + i * maxKlineNum * intervalMillis;
            long partEndTime = Math.min(partStartTime + maxKlineNum * intervalMillis, endTime) - 1;
            timeRanges.add(new TimeRange(partStartTime, partEndTime));
        }
        return timeRanges;
    }

    /**
     * 计算一段时间内的K线周期总数
     */
    private static long calculateKlineNum(long startMillis, long endMillis, IntervalEnum interval) {
        IntervalEnum.validateEnum(interval);
        if (startMillis >= endMillis) {
            throw new RuntimeException("开始时间必须小于结束时间");
        }
        long intervalMillis = IntervalEnum.INTERVAL_MILLIS_MAP.get(interval);
        return (endMillis - startMillis) / intervalMillis;
    }

    /**
     * 获取此k线周期最后一个k的布尔中轨 同步币安保留8位
     */
    public static BigDecimal getBollMiddle(List<Kline> kLines) {
        return kLines.stream().map(Kline::getClosePrice).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(kLines.size()), CommonConstant.BINANCE_PRICE_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 获取此k线周期除去最后一个k的成交量均值 同步币安保留8位
     */
    public static BigDecimal getMean(List<Kline> kLines) {
        if (kLines != null && kLines.size() > 1) {
            List<Kline> kLinesWithoutLast = kLines.subList(0, kLines.size() - 1);
            return kLinesWithoutLast.stream().map(Kline::getQuoteVolume).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(kLines.size()), CommonConstant.BINANCE_PRICE_SCALE, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.ZERO;
        }
    }


}
