package com.exchangesbot.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.exchangesbot.interceptor.UserInfo;
import com.exchangesbot.param.ConfigParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("strategy_result")
public class StrategyResult {
    /**
     * 策略结果id
     */
    private Long id;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 币种
     */
    private String symbol;

    /**
     * 周期
     */
    @TableField("`interval`")
    private String interval;

    /**
     * 策略窗口
     */
    private Integer klineWindow;

    /**
     * 止盈百分点
     */
    private BigDecimal takeProfit;

    /**
     * 止损百分点
     */
    private BigDecimal stopLossProfit;

    /**
     * 交易模式
     */
    private String tradeMode;

    /**
     * 交易策略
     */
    private String klineStrategy;

    /**
     * 账户初始余额
     */
    private BigDecimal accountInitialBalance;

    /**
     * 账户单次交易仓位
     */
    private BigDecimal position;

    /**
     * 策略开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 策略结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 最终余额
     */
    private BigDecimal endBalance;

    /**
     * 收益率
     */
    private BigDecimal yield;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    public void setInfo(UserInfo userInfo, ConfigParam configParam, BigDecimal endBalance) {
        this.id = userInfo.getRequestId();
        this.ip = userInfo.getIp();
        this.symbol = configParam.getSymbol();
        this.interval = configParam.getInterval().getValue();
        this.klineWindow = configParam.getKlineWindow();
        this.takeProfit = configParam.getTakeProfit();
        this.stopLossProfit = configParam.getStopLossProfit();
        this.tradeMode = configParam.getTradeMode().getDesc();
        this.klineStrategy = configParam.getKlineStrategy().getDesc();
        this.accountInitialBalance = configParam.getAccountInitialBalance();
        this.position = configParam.getPosition();
        this.startTime = DateUtil.parseLocalDateTime(configParam.getStartTime());
        this.endTime = DateUtil.parseLocalDateTime(configParam.getEndTime());
        this.creator = userInfo.getIp();
        this.endBalance = endBalance;
        this.yield = endBalance.subtract(accountInitialBalance).divide(accountInitialBalance, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

}