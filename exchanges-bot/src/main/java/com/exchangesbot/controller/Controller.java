package com.exchangesbot.controller;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blate.reponse.Resp;
import com.exchangesbot.entity.StrategyResult;
import com.exchangesbot.interceptor.UserInfoContext;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.param.DataPageReq;
import com.exchangesbot.resp.StrategyResultResp;
import com.exchangesbot.service.AsyncExecutor;
import com.exchangesbot.service.StrategyResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping()
@Slf4j
@RequiredArgsConstructor
public class Controller {

    private final AsyncExecutor asyncExecutor;
    private final StrategyResultService strategyResultService;

    @PostMapping("/updateConfig")
    public Resp updateConfig(@RequestBody ConfigParam configParam) {
        log.info("获取参数 {}", configParam);
        configParam.setStartTime(convertToBeijingTime(configParam.getStartTime()));
        configParam.setEndTime(convertToBeijingTime(configParam.getEndTime()));
        asyncExecutor.execute(configParam, UserInfoContext.getUserInfo());
        return Resp.success("已经开始执行");
    }

    @PostMapping("/dataList")
    public Resp selectAll(@RequestBody DataPageReq req) {
        log.info("获取分页数据-> {}", req);
        Long pageNo = req.getPageNo();
        Long pageSize = req.getPageSize();
        Page<StrategyResult> page = strategyResultService.page(new Page<>(pageNo, pageSize), new QueryWrapper<StrategyResult>().orderByDesc("yield").orderByDesc("create_time"));
        log.info("分页数据：" + page.getRecords());
        return Resp.success(new StrategyResultResp(page.getCurrent(), page.getTotal(), page.getRecords()));
    }

    public String convertToBeijingTime(String time) {
        DateTime dateTime;
        if (time.endsWith("Z")) {
            dateTime = DateUtil.parse(time, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            dateTime = dateTime.offsetNew(DateField.of(java.util.Calendar.HOUR), 8);
        } else {
            dateTime = DateUtil.parse(time, "yyyy-MM-dd HH:mm:ss");
        }
        return dateTime.toString("yyyy-MM-dd HH:mm:ss");
    }

}
