package com.exchangesbot;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@SpringBootApplication
@EnableWebSocket
@EnableAsync
@ComponentScan(basePackages = {"com.exchangesbot", "com.blate"})
public class ExchangesBotApplication {

    public static void main(String[] args) {

        SpringApplication.run(ExchangesBotApplication.class, args);

    }


}
