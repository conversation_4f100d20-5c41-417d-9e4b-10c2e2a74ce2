package com.exchangesbot.bot;


import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.eventProcess.EventDispatcher;
import com.exchangesbot.eventProcess.event.KlineProcessEvent;
import com.exchangesbot.interceptor.UserInfo;
import com.exchangesbot.interceptor.UserInfoContext;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.service.KlineFetch;
import com.exchangesbot.service.factory.KlineFetchFactory;
import com.exchangesbot.service.factory.KlineProcessEventFactory;
import com.exchangesbot.service.impl.KlineCollectorImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 币安Bot核心入口
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BinanceBot {

    private final EventDispatcher eventDispatcher;

    /**
     * 数据源 -> k线采集器 -> k线收集器 -> k线处理器
     *
     * @param configParam
     */
    public void start(ConfigParam configParam, UserInfo userInfo) {
        UserInfoContext.setUserInfo(userInfo);
        //初始化虚拟账户 和交易仓位
        userInfo.getAccountManage().initBalance(configParam.getAccountInitialBalance(), configParam.getPosition());

        //初始化k线策略处理器
        KlineStrategyEnum klineStrategy = configParam.getKlineStrategy();
        List<KlineStrategyEnum> strategyList = new ArrayList<>(List.of(klineStrategy));
        strategyList.add(KlineStrategyEnum.TAKE_STOP_LOSS);
        List<KlineProcessEvent> klineProcessList = KlineProcessEventFactory.getKlineProcessList(strategyList);
        klineProcessList.forEach(klineProcessEvent -> klineProcessEvent.setConfigParam(configParam).setUserInfo(userInfo));

        //k线收集器添加一系列k线策略处理器
        KlineCollectorImpl klineCollector = new KlineCollectorImpl(eventDispatcher);
        klineCollector.addKlineProcessEventList(klineProcessList);

        //初始化k线采集器开始采集
        KlineFetch klineFetch = KlineFetchFactory.getKlineFetch(configParam.getTradeMode().getDateSource());

        //开始采集
        klineFetch.fetchKline(configParam, klineCollector);
    }

}