package com.exchangesbot.interceptor;

import com.exchangesbot.service.AccountManage;
import com.exchangesbot.service.OrderManage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo {

    private long requestId;
    private String ip;
    private AccountManage accountManage;
    private OrderManage orderManage;

}
