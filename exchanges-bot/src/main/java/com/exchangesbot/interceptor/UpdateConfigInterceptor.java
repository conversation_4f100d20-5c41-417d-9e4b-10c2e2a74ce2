package com.exchangesbot.interceptor;

import com.blate.util.IpUntil;
import com.exchangesbot.service.impl.OrderManageImpl;
import com.exchangesbot.service.impl.VirtualAccountManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@Component
@Slf4j
public class UpdateConfigInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String requestURI = request.getRequestURI();
        String ip = IpUntil.getIp();
        log.info("拦截信息({})，uri={}", ip, requestURI);
        VirtualAccountManage accountManage = new VirtualAccountManage();
        OrderManageImpl orderManage = new OrderManageImpl();
        UserInfo userInfo = new UserInfo(System.currentTimeMillis(), ip, accountManage, orderManage);
        UserInfoContext.setUserInfo(userInfo);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) {
        UserInfoContext.clear();
    }

}
