package com.exchangesbot.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    NO_PROCESS("noProcess", "未处理"),
    DOING("doing", "处理中"),
    FINISH("finish", "已完成"),
    TAKE("take", "止盈"),
    STOP_LOSS("stopLoss", "止损"),
    NO_BALANCE("noBalance", "余额不足"),
    ;
    private final String type;
    private final String desc;
}