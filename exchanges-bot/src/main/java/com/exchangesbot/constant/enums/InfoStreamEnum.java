package com.exchangesbot.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * websocket 客户端订阅信息类型
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Getter
@AllArgsConstructor
public enum InfoStreamEnum {

    EXCHANGE_BOT("exchangeBot", "交易所机器人数据流"),

    ;

    private final String code;

    private final String desc;

    public static final Set<InfoStreamEnum> ALL_ENUMS = Arrays.stream(InfoStreamEnum.values()).collect(Collectors.toSet());
    public static final Set<String> ALL_CODES = Arrays.stream(InfoStreamEnum.values()).map(InfoStreamEnum::getCode).collect(Collectors.toSet());


    public static String validateCode(String code) {
        return !ALL_CODES.contains(code) ? "不支持的订阅流：" + code + "，请使用以下订阅流：" + ALL_CODES : StringUtils.EMPTY;
    }

    public static InfoStreamEnum getEnumByCode(String code) {
        for (InfoStreamEnum streamEnum : ALL_ENUMS) {
            if (streamEnum.getCode().equals(code)) {
                return streamEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        for (InfoStreamEnum streamEnum : ALL_ENUMS) {
            if (streamEnum.getCode().equals(code)) {
                return streamEnum.getDesc();
            }
        }
        return "未知的订阅流：" + code;
    }

}
