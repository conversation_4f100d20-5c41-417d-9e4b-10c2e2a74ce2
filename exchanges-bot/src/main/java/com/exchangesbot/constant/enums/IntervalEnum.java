package com.exchangesbot.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum IntervalEnum {
    ONE_SECOND("1s"),
    ONE_MINUTE("1m"),
    THREE_MINUTES("3m"),
    FIVE_MINUTES("5m"),
    FIFTEEN_MINUTES("15m"),
    THIRTY_MINUTES("30m"),
    ONE_HOUR("1h"),
    TWO_HOURS("2h"),
    FOUR_HOURS("4h"),
    SIX_HOURS("6h"),
    EIGHT_HOURS("8h"),
    TWELVE_HOURS("12h"),
    ONE_DAY("1d"),
    THREE_DAYS("3d"),
    ONE_WEEK("1w"),
    ONE_MONTH("1M");
    private final String value;
    public static final Set<IntervalEnum> ALL_ENUMS = Arrays.stream(IntervalEnum.values()).collect(Collectors.toSet());
    public static final Map<IntervalEnum, Long> INTERVAL_MILLIS_MAP = Map.ofEntries(
            Map.entry(ONE_SECOND, TimeUnit.SECONDS.toMillis(1)),
            Map.entry(ONE_MINUTE, TimeUnit.MINUTES.toMillis(1)),
            Map.entry(THREE_MINUTES, TimeUnit.MINUTES.toMillis(3)),
            Map.entry(FIVE_MINUTES, TimeUnit.MINUTES.toMillis(5)),
            Map.entry(FIFTEEN_MINUTES, TimeUnit.MINUTES.toMillis(15)),
            Map.entry(THIRTY_MINUTES, TimeUnit.MINUTES.toMillis(30)),
            Map.entry(ONE_HOUR, TimeUnit.HOURS.toMillis(1)),
            Map.entry(TWO_HOURS, TimeUnit.HOURS.toMillis(2)),
            Map.entry(FOUR_HOURS, TimeUnit.HOURS.toMillis(4)),
            Map.entry(SIX_HOURS, TimeUnit.HOURS.toMillis(6)),
            Map.entry(EIGHT_HOURS, TimeUnit.HOURS.toMillis(8)),
            Map.entry(TWELVE_HOURS, TimeUnit.HOURS.toMillis(12)),
            Map.entry(ONE_DAY, TimeUnit.DAYS.toMillis(1)),
            Map.entry(THREE_DAYS, TimeUnit.DAYS.toMillis(3)),
            Map.entry(ONE_WEEK, TimeUnit.DAYS.toMillis(7)),
            Map.entry(ONE_MONTH, TimeUnit.DAYS.toMillis(30))
    );

    public static void validateEnum(IntervalEnum interval) {
        if (!ALL_ENUMS.contains(interval)) {
            throw new IllegalArgumentException("不支持的周期：" + interval + "，请使用以下周期：" + ALL_ENUMS);
        }
    }

}