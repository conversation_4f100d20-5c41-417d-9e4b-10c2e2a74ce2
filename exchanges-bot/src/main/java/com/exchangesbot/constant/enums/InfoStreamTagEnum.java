package com.exchangesbot.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * websocket 客户端订阅信息tag类型
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Getter
@AllArgsConstructor
public enum InfoStreamTagEnum {

    HTTP_PROGRESS_BAR("httpProgressBar", "http进度条"),
    WEBSOCKET_PROGRESS_BAR("websocketProgressBar", "websocket进度条"),
    POSITION("position", "仓位数据"),

    ;

    private final String code;

    private final String desc;

    public static final Set<InfoStreamTagEnum> ALL_ENUMS = Arrays.stream(InfoStreamTagEnum.values()).collect(Collectors.toSet());
    public static final Set<String> ALL_CODES = Arrays.stream(InfoStreamTagEnum.values()).map(InfoStreamTagEnum::getCode).collect(Collectors.toSet());


    public static String validateCode(String code) {
        return !ALL_CODES.contains(code) ? "不支持的订阅流：" + code + "，请使用以下订阅流：" + ALL_CODES : StringUtils.EMPTY;
    }

    public static InfoStreamTagEnum getEnumByCode(String code) {
        for (InfoStreamTagEnum streamEnum : ALL_ENUMS) {
            if (streamEnum.getCode().equals(code)) {
                return streamEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        for (InfoStreamTagEnum streamEnum : ALL_ENUMS) {
            if (streamEnum.getCode().equals(code)) {
                return streamEnum.getDesc();
            }
        }
        return "未知的订阅流：" + code;
    }

}
