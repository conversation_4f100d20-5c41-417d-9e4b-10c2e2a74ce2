package com.exchangesbot.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 交易模式
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Getter
@AllArgsConstructor
public enum TradeModeEnum {
    HISTORY("history", "历史验证"),
    NOW("now", "实盘交易"),
    ;
    private final String type;
    private final String desc;

    private static final Map<TradeModeEnum, DateSourceTypeEnum> MAP = new HashMap<>();

    static {
        MAP.put(HISTORY, DateSourceTypeEnum.HTTP);
        MAP.put(NOW, DateSourceTypeEnum.WEBSOCKET);
    }

    public DateSourceTypeEnum getDateSource() {
        return MAP.get(this);
    }

}
