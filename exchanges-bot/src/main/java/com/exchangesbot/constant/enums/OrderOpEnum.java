package com.exchangesbot.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum OrderOpEnum {
    BUY("buy", "买入"),
    SELL("sell", "卖出");
    private final String type;
    private final String desc;
}