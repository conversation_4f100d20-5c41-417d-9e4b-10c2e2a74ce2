package com.exchangesbot.service;

import com.exchangesbot.dto.Kline;
import com.exchangesbot.eventProcess.event.KlineProcessEvent;

import java.util.List;

/**
 * K线收集器
 *
 * <AUTHOR>
 * @since 2025/1/9
 */

public interface KlineCollector {
    void addKlineList(List<Kline> klineList);

    List<Kline> getKlineList();

    void notifyAllObserver(List<Kline> klineList);

    void addKlineProcessEvent(KlineProcessEvent klineProcessEvent);

    void addKlineProcessEventList(List<KlineProcessEvent> klineProcessEventList);

    void removeKlineProcessEvent(KlineProcessEvent klineProcessEvent);
}