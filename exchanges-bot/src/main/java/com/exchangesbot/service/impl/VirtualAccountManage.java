package com.exchangesbot.service.impl;


import com.exchangesbot.constant.CommonConstant;
import com.exchangesbot.dto.Account;
import com.exchangesbot.service.AccountManage;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 模拟账户
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Slf4j
@Component
@NoArgsConstructor
public class VirtualAccountManage implements AccountManage {
    private BigDecimal balance;
    private BigDecimal position;
    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public void initBalance(BigDecimal amount, BigDecimal position) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("余额必须大于等于0");
        }
        this.balance = amount;
        this.position = position;
        log.info(CommonConstant.ACCOUNT_CHANGE + "初始余额：{} 单次执行仓位：{}", balance, position);
    }

    @Override
    public BigDecimal getPosition() {
        return position;
    }

    @Override
    public BigDecimal getBalance() {
        lock.lock();
        try {
            return balance;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean inMoney(Account account) {
        BigDecimal amount = account.getAmount();
        String orderId = account.getOrderId();
        String changeSource = account.getChangeSource();
        boolean flag;
        lock.lock();
        try {
            balance = balance.add(amount);
            flag = true;
            log.info(changeSource + orderId + CommonConstant.ACCOUNT_CHANGE + "收入：{} 当前余额：{}", amount, balance);
        } finally {
            lock.unlock();
        }
        return flag;
    }

    @Override
    public boolean outMoney(Account account) {
        BigDecimal amount = account.getAmount();
        String orderId = account.getOrderId();
        String changeSource = account.getChangeSource();
        boolean flag;
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("支出参数有误");
        }
        lock.lock();
        try {
            if (amount.compareTo(balance) > 0) {
                throw new RuntimeException("余额不足,无法扣除，当前余额：" + balance);
            } else {
                balance = balance.subtract(amount);
                flag = true;
                log.info(changeSource + orderId + CommonConstant.ACCOUNT_CHANGE + "支出：{} 当前余额：{}", amount, balance);
            }
        } finally {
            lock.unlock();
        }
        return flag;
    }

}
