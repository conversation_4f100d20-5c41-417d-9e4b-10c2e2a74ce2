package com.exchangesbot.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.blate.util.UidGenerator;
import com.exchangesbot.constant.CommonConstant;
import com.exchangesbot.constant.enums.*;
import com.exchangesbot.dto.Account;
import com.exchangesbot.dto.Kline;
import com.exchangesbot.dto.Order;
import com.exchangesbot.dto.Position;
import com.exchangesbot.entity.StrategyResult;
import com.exchangesbot.interceptor.UserInfo;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.resp.ExchangeMSg;
import com.exchangesbot.resp.WsMsg;
import com.exchangesbot.service.AccountManage;
import com.exchangesbot.service.OrderManage;
import com.exchangesbot.service.PositionManage;
import com.exchangesbot.service.StrategyResultService;
import com.exchangesbot.websocket.WsServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.CannotCreateTransactionException;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
public class PositionManageImpl implements PositionManage {

    private final StrategyResultService strategyResultService;

    @Override
    public void operation(Position position, UserInfo userInfo) {
        AccountManage accountManage = userInfo.getAccountManage();
        OrderManage orderManage = userInfo.getOrderManage();

        String klineStrategyEnum = position.getKlineStrategyEnum().getDesc();
        OrderOpEnum op = position.getOp();
        ConfigParam configParam = position.getConfigParam();
        Kline kline = position.getKline();

        DateTime date = DateUtil.date();

        if (op == OrderOpEnum.BUY) {
            BigDecimal buyPrice = position.getOpPrice();
            BigDecimal takePrice = calculateTake(buyPrice, configParam.getTakeProfit(), CommonConstant.BINANCE_PRICE_SCALE);
            BigDecimal stopLossPrice = calculateStopLoss(buyPrice, configParam.getStopLossProfit(), CommonConstant.BINANCE_PRICE_SCALE);
            BigDecimal accountBalance = accountManage.getBalance();
            BigDecimal defaultBalance = accountManage.getPosition();
//            BigDecimal balance = accountBalance.compareTo(defaultBalance) > 0 || accountBalance.compareTo(new BigDecimal(0)) == 0 ? defaultBalance : accountBalance;
            BigDecimal balance = defaultBalance;

            Order order = new Order(UidGenerator.getUid(), date, OrderOpEnum.BUY, balance, OrderStatusEnum.FINISH, buyPrice, takePrice, stopLossPrice, null, null, klineStrategyEnum);
            Account account = new Account(order.getOrderId(), date, AccountOpEnum.OUT, balance, AccountStatusEnum.FINISH, klineStrategyEnum);
            try {
                if (orderManage.addOrder(order) && accountManage.outMoney(account)) {
                    ExchangeMSg exchangeMSg = new ExchangeMSg(klineStrategyEnum,
                            PositionOpEnum.OPEN_SUCCESS.getDesc(),
                            order.getOrderStatus().getDesc(),
                            DateUtil.date(kline.getOpenTime()),
                            order.getOrderId(),
                            account.getAmount(),
                            buyPrice, takePrice, stopLossPrice,
                            accountManage.getBalance()
                    );
                    WsMsg wsMsg = new WsMsg(exchangeMSg);
                    wsMsg.setIp(userInfo.getIp());
                    WsServer.pushInfoStream(wsMsg);

                    StrategyResult strategyResult = new StrategyResult();
                    strategyResult.setInfo(userInfo, configParam, accountManage.getBalance());
                    strategyResultService.saveOrUpdate(strategyResult);
                    log.info("【{}】开单成功！订单号：{} 开盘时间：{}", klineStrategyEnum, order.getOrderId(), DateUtil.date(kline.getOpenTime()));
                }
            } catch (CannotCreateTransactionException e) {
                e.printStackTrace();
                log.error("【{}】数据库连接异常！订单号：{} 开盘时间：{} {}", klineStrategyEnum, order.getOrderId(), DateUtil.date(kline.getOpenTime()), e.getMessage());
            } catch (Exception e){
                e.printStackTrace();
                order.setOrderStatus(OrderStatusEnum.NO_BALANCE);
                log.error("【{}】开单失败！订单号：{} 开盘时间：{} {}", klineStrategyEnum, order.getOrderId(), DateUtil.date(kline.getOpenTime()), e.getMessage());
                ExchangeMSg exchangeMSg = new ExchangeMSg(klineStrategyEnum,
                        PositionOpEnum.OPEN_FAIL.getDesc(),
                        order.getOrderStatus().getDesc(),
                        DateUtil.date(kline.getOpenTime()),
                        order.getOrderId(),
                        order.getAmount(),
                        buyPrice, takePrice, stopLossPrice,
                        accountManage.getBalance()
                );
                WsMsg wsMsg = new WsMsg(exchangeMSg);
                wsMsg.setIp(userInfo.getIp());
                WsServer.pushInfoStream(wsMsg);
            }

        } else if (op == OrderOpEnum.SELL) {
            Set<Order> orderSet = orderManage.getOrderSet();
            if (ObjectUtils.isEmpty(orderSet)) {
                return;
            }
            for (Order order : orderSet) {
                if (isBuyOrderCompleted(order)) {
                    BigDecimal amount = order.getAmount();
                    BigDecimal takePrice = order.getTakePrice();
                    BigDecimal stopLossPrice = order.getStopLossPrice();
                    TradeModeEnum tradeMode = configParam.getTradeMode();
                    Account account = new Account(order.getOrderId(), date, AccountOpEnum.IN, null, AccountStatusEnum.FINISH, klineStrategyEnum);
                    // 判断是否需要止盈
                    if (shouldTakeProfit(kline, takePrice, tradeMode)) {
                        account.setAmount(calculateTake(amount, configParam.getTakeProfit(), CommonConstant.VIRTUAL_ACCOUNT_AMOUNT_SCALE));
                        accountManage.inMoney(account);
                        order.setOrderStatus(OrderStatusEnum.TAKE);
                        log.info("【{}】平单成功({})-止盈!", klineStrategyEnum, order.getOrderId());
                        ExchangeMSg exchangeMSg = new ExchangeMSg(klineStrategyEnum,
                                PositionOpEnum.CLOSE_SUCCESS.getDesc(),
                                order.getOrderStatus().getDesc(),
                                DateUtil.date(kline.getOpenTime()),
                                order.getOrderId(),
                                account.getAmount(),
                                takePrice, null, null,
                                accountManage.getBalance()
                        );
                        WsMsg wsMsg = new WsMsg(exchangeMSg);
                        wsMsg.setIp(userInfo.getIp());
                        WsServer.pushInfoStream(wsMsg);

                        StrategyResult strategyResult = new StrategyResult();
                        strategyResult.setInfo(userInfo, configParam, accountManage.getBalance());
                        strategyResultService.saveOrUpdate(strategyResult);

                    }
                    // 判断是否需要止损
                    else if (shouldStopLoss(kline, stopLossPrice, tradeMode)) {
                        account.setAmount(calculateStopLoss(amount, configParam.getStopLossProfit(), CommonConstant.VIRTUAL_ACCOUNT_AMOUNT_SCALE));
                        accountManage.inMoney(account);
                        order.setOrderStatus(OrderStatusEnum.STOP_LOSS);
                        ExchangeMSg exchangeMSg = new ExchangeMSg(klineStrategyEnum,
                                PositionOpEnum.CLOSE_SUCCESS.getDesc(),
                                order.getOrderStatus().getDesc(),
                                DateUtil.date(kline.getOpenTime()),
                                order.getOrderId(),
                                account.getAmount(),
                                takePrice, null, null,
                                accountManage.getBalance()
                        );
                        WsMsg wsMsg = new WsMsg(exchangeMSg);
                        wsMsg.setIp(userInfo.getIp());
                        WsServer.pushInfoStream(wsMsg);

                        StrategyResult strategyResult = new StrategyResult();
                        strategyResult.setInfo(userInfo, configParam, accountManage.getBalance());
                        strategyResultService.saveOrUpdate(strategyResult);
                        log.info("【{}】平单成功({})-止损!", klineStrategyEnum, order.getOrderId());
                    }
                }
            }
        } else {
            log.error("【{}】未知操作！", klineStrategyEnum);
        }

    }

    /**
     * 判断订单是否已完成且是买入订单
     */
    private boolean isBuyOrderCompleted(Order order) {
        return order.getOrderStatus().equals(OrderStatusEnum.FINISH) && order.getOrderOp().equals(OrderOpEnum.BUY);
    }

    /**
     * 计算止盈
     */
    public static BigDecimal calculateTake(BigDecimal price, BigDecimal takeProfit, int scale) {
        BigDecimal one = new BigDecimal("1");
        BigDecimal takeProfitMultiplier = one.add(takeProfit.divide(new BigDecimal("100"), scale, RoundingMode.HALF_UP));
        return price.multiply(takeProfitMultiplier);
    }

    /**
     * 计算止损
     */
    public static BigDecimal calculateStopLoss(BigDecimal price, BigDecimal stopLossProfit, int scale) {
        BigDecimal one = new BigDecimal("1");
        BigDecimal stopLossMultiplier = one.subtract(stopLossProfit.divide(new BigDecimal("100"), scale, RoundingMode.HALF_UP));
        return price.multiply(stopLossMultiplier);
    }


    /**
     * 判断是否需要止盈
     */
    private boolean shouldTakeProfit(Kline currentKline, BigDecimal takePrice, TradeModeEnum tradeMode) {
        return switch (tradeMode) {
            case HISTORY -> currentKline.getHighPrice().compareTo(takePrice) >= 0;
            case NOW -> currentKline.getClosePrice().compareTo(takePrice) >= 0;
        };
    }

    /**
     * 判断是否需要止损
     */
    private boolean shouldStopLoss(Kline currentKline, BigDecimal stopLossPrice, TradeModeEnum tradeMode) {
        return switch (tradeMode) {
            case HISTORY -> currentKline.getLowPrice().compareTo(stopLossPrice) <= 0;
            case NOW -> currentKline.getClosePrice().compareTo(stopLossPrice) <= 0;
        };
    }

}
