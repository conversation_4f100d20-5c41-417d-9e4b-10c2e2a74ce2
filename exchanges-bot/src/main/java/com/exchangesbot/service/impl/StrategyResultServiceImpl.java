package com.exchangesbot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exchangesbot.entity.StrategyResult;
import com.exchangesbot.mapper.StrategyResultMapper;
import com.exchangesbot.service.StrategyResultService;
import org.springframework.stereotype.Service;

@Service
public class StrategyResultServiceImpl extends ServiceImpl<StrategyResultMapper, StrategyResult> implements StrategyResultService {

}