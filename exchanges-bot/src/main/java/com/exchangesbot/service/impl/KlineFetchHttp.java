package com.exchangesbot.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.blate.dto.OkhttpResp;
import com.blate.util.OkhttpUtils;
import com.blate.util.TimeUtils;
import com.exchangesbot.constant.enums.InfoStreamEnum;
import com.exchangesbot.constant.enums.InfoStreamTagEnum;
import com.exchangesbot.constant.enums.IntervalEnum;
import com.exchangesbot.dto.Kline;
import com.exchangesbot.dto.TimeRange;
import com.exchangesbot.interceptor.UserInfoContext;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.resp.WsMsg;
import com.exchangesbot.resp.httpProgressBar;
import com.exchangesbot.service.KlineCollector;
import com.exchangesbot.service.KlineFetch;
import com.exchangesbot.until.KlineCalculateUtil;
import com.exchangesbot.websocket.WsServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


@Slf4j
@Component
@RequiredArgsConstructor
public class KlineFetchHttp implements KlineFetch {
    private static final String BASE_URL = "https://api.binance.com";
    private static final String URL_PATH = "/api/v3/uiKlines";
    private static final Integer BINANCE_KLINE_MAX = 1000;
    public static final String SYMBOL = "symbol";
    public static final String INTERVAL = "interval";
    public static final String LIMIT = "limit";
    public static final String START_TIME = "startTime";
    public static final String END_TIME = "endTime";
    public static final long SLEEP_TIME = 0;

    @Override
    public void fetchKline(ConfigParam configParam, KlineCollector klineCollector) {
        List<Kline> klineList = getKlineList(configParam);
        Integer klineWindowNum = configParam.getKlineWindow();
        int size = klineList.size();
        if (size < klineWindowNum) {
            klineCollector.addKlineList(klineList);
            return;
        }
        for (int i = 0; i <= klineList.size() - klineWindowNum; i++) {
            int j = i + klineWindowNum;
            List<Kline> window = klineList.subList(i, j);
            TimeUtils.sleep(SLEEP_TIME);

            klineCollector.addKlineList(window);

            WsMsg wsMsg = new WsMsg(InfoStreamEnum.EXCHANGE_BOT, InfoStreamTagEnum.HTTP_PROGRESS_BAR, new httpProgressBar(j, size));
            wsMsg.setIp(UserInfoContext.getUserInfo().getIp());
            WsServer.pushInfoStream(wsMsg);
            // BLATE-TODO 2025/2/5 增加停止按钮
        }
    }

    private List<Kline> getKlineList(ConfigParam configParam) {
        String startTime = configParam.getStartTime();
        String endTime = configParam.getEndTime();
        IntervalEnum interval = configParam.getInterval();
        if (ObjectUtils.isEmpty(startTime) || ObjectUtils.isEmpty(endTime)) {
            throw new RuntimeException("开始时间和结束时间不能为空");
        }
        Integer bollMiddleCycle = configParam.getKlineWindow();
        long shouldStartTime = KlineCalculateUtil.getShouldStartTime(startTime, interval, bollMiddleCycle);
        long shouldEndTime = DateUtil.parse(endTime).getTime();
        List<TimeRange> timeRanges = KlineCalculateUtil.calculateTimeRanges(shouldStartTime, shouldEndTime, interval, BINANCE_KLINE_MAX);
        timeRanges.forEach(range -> log.info("拆分周期为：{} - {}", DateUtil.date(range.getStartTime()), DateUtil.date(range.getEndTime())));
        List<Kline> klineList = new ArrayList<>();
        for (TimeRange range : timeRanges) {
            klineList.addAll(requestKline(configParam.getSymbol(), range.getStartTime(), range.getEndTime(), interval));
        }
        log.info("本次实际获取{}条K线", klineList.size());
        return klineList;
    }

    public List<Kline> requestKline(String symbol, long startTime, long endTime, IntervalEnum interval) {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put(SYMBOL, symbol);
        parameters.put(START_TIME, startTime);
        parameters.put(END_TIME, endTime);
        parameters.put(INTERVAL, interval.getValue());
        parameters.put(LIMIT, BINANCE_KLINE_MAX);
        OkhttpResp okhttpResp = OkhttpUtils.get(OkhttpUtils.buildUrl(BASE_URL, URL_PATH, parameters));
        return convertToKlineList(okhttpResp.getJsonString());
    }

    @Override
    public List<Kline> convertToKlineList(String data) {
        JSONArray jsonArray = JSONArray.parseArray(data);
        List<Kline> res = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray jA = jsonArray.getJSONArray(i);
            res.add(new Kline(
                    jA.getLong(0),
                    jA.getBigDecimal(1),
                    jA.getBigDecimal(2),
                    jA.getBigDecimal(3),
                    jA.getBigDecimal(4),
                    jA.getBigDecimal(5),
                    jA.getLong(6),
                    jA.getBigDecimal(7),
                    jA.getBigDecimal(8),
                    jA.getBigDecimal(9),
                    jA.getBigDecimal(10)
            ));
        }
        return res;
    }

}
