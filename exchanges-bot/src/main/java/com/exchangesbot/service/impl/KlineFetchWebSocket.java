package com.exchangesbot.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.blate.util.OkhttpUtils;
import com.exchangesbot.constant.enums.InfoStreamEnum;
import com.exchangesbot.constant.enums.InfoStreamTagEnum;
import com.exchangesbot.dto.Kline;
import com.exchangesbot.interceptor.UserInfoContext;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.resp.WebSocketProgressBar;
import com.exchangesbot.resp.WsMsg;
import com.exchangesbot.service.KlineCollector;
import com.exchangesbot.service.KlineFetch;
import com.exchangesbot.websocket.WsServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.WebSocketConnectionManager;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@EnableWebSocket
@Component
@Slf4j
public class KlineFetchWebSocket extends TextWebSocketHandler implements KlineFetch {
    private static final String BASE_URL = "wss://stream.binance.com:443/ws/";
    private static final String URL_PATH = "@kline_";
    @Resource
    private WebSocketConnectionManager connectionManager;
    @Resource
    private WebSocketClient webSocketClient;
    @Resource
    private KlineCollectorImpl klineCollector;

    @Override
    public void fetchKline(ConfigParam configParam, KlineCollector klineCollector) {
        this.connectionManager = new WebSocketConnectionManager(webSocketClient, this, OkhttpUtils.buildUrl(BASE_URL, configParam.getSymbol().toLowerCase() + URL_PATH + configParam.getInterval().getValue(), null));
        connectionManager.start();
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) {
        String payload = (String) message.getPayload();
        List<Kline> klineList = convertToKlineList(payload);

        klineCollector.addKlineList(klineList);

        WsMsg wsMsg = new WsMsg(InfoStreamEnum.EXCHANGE_BOT, InfoStreamTagEnum.WEBSOCKET_PROGRESS_BAR, new WebSocketProgressBar("持续运行中"));
        wsMsg.setIp(UserInfoContext.getUserInfo().getIp());
        WsServer.pushInfoStream(wsMsg);

        // BLATE-TODO 2025/2/5 增加停止按钮
    }

    @Override
    public List<Kline> convertToKlineList(String data) {
        JSONObject jsonObject = JSONObject.parseObject(data);
        List<Kline> res = new ArrayList<>();
        JSONObject jo = jsonObject.getJSONObject("k");
        res.add(new Kline(
                jo.getLong("t"),
                jo.getBigDecimal("o"),
                jo.getBigDecimal("h"),
                jo.getBigDecimal("l"),
                jo.getBigDecimal("c"),
                jo.getBigDecimal("v"),
                jo.getLong("T"),
                jo.getBigDecimal("q"),
                jo.getBigDecimal("n"),
                jo.getBigDecimal("V"),
                jo.getBigDecimal("Q")
        ));
        return res;
    }

}
