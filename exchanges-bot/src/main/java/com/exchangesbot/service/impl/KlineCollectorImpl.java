package com.exchangesbot.service.impl;


import com.exchangesbot.dto.Kline;
import com.exchangesbot.eventProcess.EventDispatcher;
import com.exchangesbot.eventProcess.event.KlineProcessEvent;
import com.exchangesbot.service.KlineCollector;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;

@Component
@RequiredArgsConstructor
public class KlineCollectorImpl implements KlineCollector {
    private final TreeMap<Long, Kline> kLineMap = new TreeMap<>();
    private final List<KlineProcessEvent> events = new ArrayList<>();
    private final EventDispatcher eventDispatcher;

    @Override
    public synchronized void addKlineList(List<Kline> klineList) {
        for (Kline kLine : klineList) {
            kLineMap.put(kLine.getOpenTime(), kLine);
        }
        notifyAllObserver(getKlineList());
    }

    @Override
    public List<Kline> getKlineList() {
        return new ArrayList<>(kLineMap.values());
    }

    @Override
    public void notifyAllObserver(List<Kline> klineList) {
        events.forEach(event -> event.setKlineList(klineList));
        eventDispatcher.publish(events);
    }

    @Override
    public void addKlineProcessEvent(KlineProcessEvent klineProcessEvent) {
        events.add(klineProcessEvent);
    }

    @Override
    public void addKlineProcessEventList(List<KlineProcessEvent> klineProcessEventList) {
        klineProcessEventList.forEach(this::addKlineProcessEvent);
    }

    @Override
    public void removeKlineProcessEvent(KlineProcessEvent klineProcessEvent) {
        events.remove(klineProcessEvent);
    }

}