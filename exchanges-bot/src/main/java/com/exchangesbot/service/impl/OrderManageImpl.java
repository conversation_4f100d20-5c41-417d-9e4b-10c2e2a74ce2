package com.exchangesbot.service.impl;


import com.exchangesbot.constant.CommonConstant;
import com.exchangesbot.dto.Order;
import com.exchangesbot.service.OrderManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class OrderManageImpl implements OrderManage {
    private static final Set<Order> ORDER_SET = new CopyOnWriteArraySet<>();
    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public Set<Order> getOrderSet() {
        lock.lock();
        try {
            return ORDER_SET;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean addOrder(Order order) {
        boolean flag;
        if (ObjectUtils.isEmpty(order)) {
            throw new IllegalArgumentException("订单为空");
        }
        lock.lock();
        try {
            ORDER_SET.add(order);
            flag = true;
            log.info(order.getChangeSource() + CommonConstant.ORDER_CHANGE + "添加订单({})，目前总订单数量:{}", order.getOrderId(), ORDER_SET.size());
        } finally {
            lock.unlock();
        }
        return flag;
    }

    @Override
    public boolean removeOrder(Order order) {
        boolean flag;
        String orderId = order.getOrderId();
        if (ObjectUtils.isEmpty(ORDER_SET)) {
            throw new IllegalArgumentException("订单列表为空，无法移除，订单号：" + orderId);
        }
        lock.lock();
        try {
            ORDER_SET.remove(order);
            flag = true;
            log.info(order.getChangeSource() + CommonConstant.ORDER_CHANGE + "移除订单({})，目前总订单数量:{}", orderId, ORDER_SET.size());
        } finally {
            lock.unlock();
        }
        return flag;
    }
}
