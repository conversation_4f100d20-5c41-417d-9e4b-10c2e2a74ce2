package com.exchangesbot.service;

import com.exchangesbot.bot.BinanceBot;
import com.exchangesbot.interceptor.UserInfo;
import com.exchangesbot.param.ConfigParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncExecutor {

    private final BinanceBot binanceBot;

    @Async("defaultThreadPool")
    public void execute(ConfigParam configParam, UserInfo userInfo) {
        binanceBot.start(configParam, userInfo);
    }

}
