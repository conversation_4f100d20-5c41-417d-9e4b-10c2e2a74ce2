package com.exchangesbot.service.factory;

import com.exchangesbot.constant.enums.DateSourceTypeEnum;
import com.exchangesbot.service.KlineFetch;
import com.exchangesbot.service.impl.KlineFetchHttp;
import com.exchangesbot.service.impl.KlineFetchWebSocket;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class KlineFetchFactory {
    private static final Map<DateSourceTypeEnum, KlineFetch> KLINE_FETCH_STRATEGY = new HashMap<>();

    private final KlineFetchHttp klineFetchHttp;
    private final KlineFetchWebSocket klineFetchWebSocket;

    @PostConstruct
    public void init() {
        KLINE_FETCH_STRATEGY.put(DateSourceTypeEnum.HTTP, klineFetchHttp);
        KLINE_FETCH_STRATEGY.put(DateSourceTypeEnum.WEBSOCKET, klineFetchWebSocket);
    }

    public static KlineFetch getKlineFetch(DateSourceTypeEnum dateSourceTypeEnum) {
        KlineFetch klineFetch = KLINE_FETCH_STRATEGY.get(dateSourceTypeEnum);
        if (ObjectUtils.isEmpty(klineFetch)) {
            throw new RuntimeException("未知K线收集器!");
        }
        return klineFetch;
    }

}
