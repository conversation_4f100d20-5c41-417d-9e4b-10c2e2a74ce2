package com.exchangesbot.service.factory;

import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.eventProcess.event.BollMiddle;
import com.exchangesbot.eventProcess.event.Cross;
import com.exchangesbot.eventProcess.event.KlineProcessEvent;
import com.exchangesbot.eventProcess.event.TakeStopLoss;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 所有k线处理器记得注册到这
 */
@Component
@RequiredArgsConstructor
public class KlineProcessEventFactory {
    private static final Map<KlineStrategyEnum, KlineProcessEvent> KLINE_OBSERVER_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        KLINE_OBSERVER_MAP.put(KlineStrategyEnum.BOLL_MIDDLE, new BollMiddle());
        KLINE_OBSERVER_MAP.put(KlineStrategyEnum.TAKE_STOP_LOSS, new TakeStopLoss());
        KLINE_OBSERVER_MAP.put(KlineStrategyEnum.CROSS, new Cross());
    }

    public static KlineProcessEvent getKlineProcess(KlineStrategyEnum klineStrategyEnum) {
        return switch (klineStrategyEnum) {
            case BOLL_MIDDLE -> new BollMiddle();
            case TAKE_STOP_LOSS -> new TakeStopLoss();
            case CROSS -> new Cross();
            default -> throw new RuntimeException("未知K线处理器！");
        };
    }

    public static List<KlineProcessEvent> getKlineProcessList(List<KlineStrategyEnum> strategyList) {
        return strategyList.stream().map(KlineProcessEventFactory::getKlineProcess).toList();
    }

}
