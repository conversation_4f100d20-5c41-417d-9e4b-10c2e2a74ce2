package com.exchangesbot.resp;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeMSg {

    private String klineStrategyEnum;//策略
    private String opType;//操作类型
    private String orderStatus;//订单类型

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private DateTime klineDate;//k线操作时间

    private String orderId;//订单ID
    private BigDecimal opAmount;//操作金额 U本位

    private BigDecimal opPrice;//操作价格
    private BigDecimal takePrice;//止盈价格
    private BigDecimal stopLossPrice;//止损价格

    private BigDecimal currentBalance;//当前余额

}
