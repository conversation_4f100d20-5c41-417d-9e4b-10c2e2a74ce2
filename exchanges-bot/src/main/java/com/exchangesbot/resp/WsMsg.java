package com.exchangesbot.resp;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.annotation.JSONField;
import com.exchangesbot.constant.enums.InfoStreamEnum;
import com.exchangesbot.constant.enums.InfoStreamTagEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WsMsg {
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private DateTime time = DateTime.now();//时间
    private InfoStreamEnum stream = InfoStreamEnum.EXCHANGE_BOT;//哪个订阅流
    private String ip;//给 哪个 ip推送 不填默认 所有ip
    private InfoStreamTagEnum tag = InfoStreamTagEnum.POSITION;//消息tag（区别消息，前端自己辨别）
    private Object data;//数据

    public WsMsg(Object data) {
        this.data = data;
    }

    public WsMsg(InfoStreamEnum stream, InfoStreamTagEnum tag, Object data) {
        this.stream = stream;
        this.tag = tag;
        this.data = data;
    }
}
