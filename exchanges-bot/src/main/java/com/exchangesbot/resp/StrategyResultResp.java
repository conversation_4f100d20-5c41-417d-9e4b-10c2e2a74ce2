package com.exchangesbot.resp;

import com.exchangesbot.entity.StrategyResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class StrategyResultResp {

    private Long current;
    private Long total;
    private List<StrategyResult> dataList;

}