package com.exchangesbot.dto;


import com.exchangesbot.constant.enums.KlineTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class Kline {
    private Long openTime;        // 开盘时间
    private BigDecimal openPrice;     // 开盘价
    private BigDecimal highPrice;     // 最高价
    private BigDecimal lowPrice;      // 最低价
    private BigDecimal closePrice;    // 收盘价
    private BigDecimal volume;        // 成交量 币本位
    private Long closeTime;       // 收盘时间
    private BigDecimal quoteVolume;   // 成交额 u本位
    private BigDecimal trades;           // 成交笔数
    private BigDecimal buyVolume;     // 主动买入成交量
    private BigDecimal buyQuoteVolume; // 主动买入成交额

    private BigDecimal bollMiddle;//计算的布林中轨
    private Boolean isHighBollMiddle;//最低价是否高于布林中轨

    private KlineTypeEnum klineTypeEnum;//绿柱还是红柱
    private BigDecimal upperShadow;//上影针
    private BigDecimal lowerShadow;//下影针
    private BigDecimal column;//实体柱

    public Kline(Long openTime, BigDecimal openPrice, BigDecimal highPrice, BigDecimal lowPrice, BigDecimal closePrice, BigDecimal volume, Long closeTime, BigDecimal quoteVolume, BigDecimal trades, BigDecimal buyVolume, BigDecimal buyQuoteVolume) {
        this.openTime = openTime;
        this.openPrice = openPrice;
        this.highPrice = highPrice;
        this.lowPrice = lowPrice;
        this.closePrice = closePrice;
        this.volume = volume;
        this.closeTime = closeTime;
        this.quoteVolume = quoteVolume;
        this.trades = trades;
        this.buyVolume = buyVolume;
        this.buyQuoteVolume = buyQuoteVolume;
    }

}