package com.exchangesbot.dto;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.annotation.JSONField;
import com.exchangesbot.constant.enums.OrderOpEnum;
import com.exchangesbot.constant.enums.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Order {

    private String orderId;//订单ID

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private DateTime orderTime;//订单时间
    private OrderOpEnum orderOp;//订单操作类型
    private BigDecimal amount;//订单金额 U本位
    private OrderStatusEnum orderStatus;//订单状态
    private BigDecimal opPrice;//操作价格
    private BigDecimal takePrice;//止盈价格
    private BigDecimal stopLossPrice;//止损价格

    private String relatedOrderId;// 相关订单ID（如果是卖出订单，关联买入订单ID）
    private BigDecimal sellAmount;// 卖出金额 U本位（如果是卖出订单，记录卖出的金额）

    private String changeSource;// 变动来源

}
