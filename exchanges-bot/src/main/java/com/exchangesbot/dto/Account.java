package com.exchangesbot.dto;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.annotation.JSONField;
import com.exchangesbot.constant.enums.AccountOpEnum;
import com.exchangesbot.constant.enums.AccountStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Account {

    private String orderId;//订单ID

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private DateTime opTime;//操作时间
    private AccountOpEnum orderOp;//操作类型
    private BigDecimal amount;//操作金额 U本位
    private AccountStatusEnum status;//操作状态
    private String changeSource;// 变动来源

}
