package com.exchangesbot.dto;

import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.constant.enums.OrderOpEnum;
import com.exchangesbot.param.ConfigParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Position {

    private KlineStrategyEnum klineStrategyEnum;//操作来源
    private OrderOpEnum op;//操作
    private Date opTime;//操作时间
    private BigDecimal opPrice;//操作价格
    private BigDecimal amount;//订单金额 U本位
    private ConfigParam configParam;//基础配置
    private Kline kline;//k线

}
