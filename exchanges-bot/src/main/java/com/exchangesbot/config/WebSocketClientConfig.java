package com.exchangesbot.config;


import com.exchangesbot.service.impl.KlineFetchWebSocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.WebSocketConnectionManager;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;


/**
 * websocket 客户端
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Configuration
@Slf4j
public class WebSocketClientConfig {

    @Bean
    public WebSocketClient webSocketClient() {
        return new StandardWebSocketClient();
    }

    @Bean
    public WebSocketConnectionManager webSocketConnectionManager(WebSocketClient webSocketClient) {
        WebSocketConnectionManager connectionManager = new WebSocketConnectionManager(webSocketClient, new KlineFetchWebSocket(), "");
        connectionManager.setAutoStartup(false);
        return connectionManager;
    }

}
