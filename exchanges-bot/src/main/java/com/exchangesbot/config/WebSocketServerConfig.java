package com.exchangesbot.config;


import com.exchangesbot.websocket.WsServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * websocket 服务端
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Configuration
@EnableWebSocket
@Slf4j
public class WebSocketServerConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new WsServer(), "/v1/{wsClientInfoStream}").setAllowedOrigins("*");
        log.info("webSocket服务端启动成功！");
    }

}