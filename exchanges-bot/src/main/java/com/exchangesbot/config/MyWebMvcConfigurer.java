package com.exchangesbot.config;

import com.blate.config.BaseWebMvcConfigurer;
import com.exchangesbot.interceptor.UpdateConfigInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import javax.annotation.Resource;


@Configuration
public class MyWebMvcConfigurer extends BaseWebMvcConfigurer {

    @Resource
    private UpdateConfigInterceptor updateConfigInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(updateConfigInterceptor).addPathPatterns("/updateConfig");
    }

}