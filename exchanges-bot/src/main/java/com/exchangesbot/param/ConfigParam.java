package com.exchangesbot.param;

import com.exchangesbot.constant.enums.IntervalEnum;
import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.constant.enums.TradeModeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 策略参数配置
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigParam {

    //通用交易参数
    private String symbol;//币种
    private IntervalEnum interval;//k线周期
    private Integer klineWindow;//策略窗口
    private BigDecimal takeProfit;//止盈百分点
    private BigDecimal stopLossProfit;//止损百分点
    private TradeModeEnum tradeMode;//交易模式
    private KlineStrategyEnum klineStrategy;//交易策略

    //模拟交易参数
    private BigDecimal accountInitialBalance;//账户初始余额
    private BigDecimal position;//账户单次交易仓位
    private String startTime;//开始时间
    private String endTime;//结束时间

    @Override
    public String toString() {
        return String.format("币种：%s k线周期：%s 策略验证时间段为：%s - %s 策略周期： %s 账户初始金额： %s 止盈：%s 止损： %s",
                symbol, interval.getValue(), startTime, endTime, klineWindow, accountInitialBalance, takeProfit, stopLossProfit);
    }

}