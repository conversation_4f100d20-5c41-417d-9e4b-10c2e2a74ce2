package com.exchangesbot.eventProcess.event;


import com.exchangesbot.dto.Kline;
import com.exchangesbot.eventProcess.BaseEvent;
import com.exchangesbot.interceptor.UserInfo;
import com.exchangesbot.param.ConfigParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * K线处理器
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public abstract class KlineProcessEvent extends BaseEvent {

    private List<Kline> klineList;
    private ConfigParam configParam;

    private UserInfo userInfo;

}