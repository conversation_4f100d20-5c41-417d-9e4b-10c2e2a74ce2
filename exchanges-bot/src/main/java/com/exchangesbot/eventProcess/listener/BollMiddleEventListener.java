package com.exchangesbot.eventProcess.listener;


import cn.hutool.core.date.DateUtil;
import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.constant.enums.OrderOpEnum;
import com.exchangesbot.dto.Kline;
import com.exchangesbot.dto.Position;
import com.exchangesbot.eventProcess.EventHandler;
import com.exchangesbot.eventProcess.event.BollMiddle;
import com.exchangesbot.interceptor.UserInfoContext;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.service.PositionManage;
import com.exchangesbot.until.EventUtil;
import com.exchangesbot.until.KlineCalculateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 布尔中轨策略事件处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BollMiddleEventListener implements EventHandler<BollMiddle> {

    private final PositionManage positionManage;

    @Override
    public void handle(BollMiddle event) {
        List<Kline> klineList = event.getKlineList();
        ConfigParam configParam = event.getConfigParam();
        klineProcess(klineList, configParam);
    }

    private void klineProcess(List<Kline> klineList, ConfigParam configParam) {
        Integer klineWindowNum = configParam.getKlineWindow();

        if (klineList.size() < klineWindowNum) {
            log.info("当前k线数据小于策略窗口，不予处理");// BLATE-TODO: 2025/1/9  后期ws需要 自动补全窗口
            return;
        }

        List<Kline> window = klineList.subList(klineList.size() - klineWindowNum, klineList.size());

        Kline currentLine = window.get(window.size() - 1);
        BigDecimal lowPrice = currentLine.getLowPrice();
        BigDecimal bollMiddle = KlineCalculateUtil.getBollMiddle(window);
        currentLine.setBollMiddle(bollMiddle);
        currentLine.setIsHighBollMiddle(lowPrice.compareTo(bollMiddle) > 0);// BLATE-TODO: 2025/1/9 可以配置化百分比接近中轨1
        EventUtil.logCurrentKline(klineList, KlineStrategyEnum.BOLL_MIDDLE.getDesc());

        if (checkCondition(window)) { //生成购买订单
            positionManage.operation(new Position(KlineStrategyEnum.BOLL_MIDDLE, OrderOpEnum.BUY, DateUtil.date(), bollMiddle, null, configParam, currentLine), UserInfoContext.getUserInfo());
        }

    }

    // BLATE-TODO 2025/2/9 实时k线一直接触 会重复买
    private boolean checkCondition(List<Kline> window) {
        boolean beforeWindowOk = window.subList(0, window.size() - 1).stream().allMatch(kline -> kline.getIsHighBollMiddle() != null && kline.getIsHighBollMiddle());
        boolean isHighBollMiddle = window.get(window.size() - 1).getIsHighBollMiddle();
        return beforeWindowOk && (!isHighBollMiddle);
    }

    @Override
    public String getSupportEventType() {
        return KlineStrategyEnum.BOLL_MIDDLE.getCode();
    }

}