package com.exchangesbot.eventProcess.listener;


import cn.hutool.core.date.DateUtil;
import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.constant.enums.OrderOpEnum;
import com.exchangesbot.dto.Kline;
import com.exchangesbot.dto.Position;
import com.exchangesbot.eventProcess.EventHandler;
import com.exchangesbot.eventProcess.event.TakeStopLoss;
import com.exchangesbot.interceptor.UserInfoContext;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.service.PositionManage;
import com.exchangesbot.until.EventUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 止盈止损策略事件处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TakeStopLossEventListener implements EventHandler<TakeStopLoss> {

    private final PositionManage positionManage;

    @Override
    public void handle(TakeStopLoss event) {
        List<Kline> klineList = event.getKlineList();
        ConfigParam configParam = event.getConfigParam();
        EventUtil.logCurrentKline(klineList,KlineStrategyEnum.TAKE_STOP_LOSS.getDesc());
        klineProcess(klineList, configParam);
    }

    // BLATE-TODO: 2025/1/9 后期并发运行 快速检查所有订单迅速止盈止损
    private void klineProcess(List<Kline> klineList, ConfigParam configParam) {
        Kline currentKline = klineList.get(klineList.size() - 1);
        positionManage.operation(new Position(KlineStrategyEnum.TAKE_STOP_LOSS, OrderOpEnum.SELL, DateUtil.date(), null,null, configParam, currentKline), UserInfoContext.getUserInfo());
    }


    @Override
    public String getSupportEventType() {
        return KlineStrategyEnum.TAKE_STOP_LOSS.getCode();
    }

}