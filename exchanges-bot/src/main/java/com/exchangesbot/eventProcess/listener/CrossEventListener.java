package com.exchangesbot.eventProcess.listener;


import cn.hutool.core.date.DateUtil;
import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.constant.enums.KlineTypeEnum;
import com.exchangesbot.constant.enums.OrderOpEnum;
import com.exchangesbot.dto.Kline;
import com.exchangesbot.dto.Position;
import com.exchangesbot.eventProcess.EventHandler;
import com.exchangesbot.eventProcess.event.Cross;
import com.exchangesbot.interceptor.UserInfoContext;
import com.exchangesbot.param.ConfigParam;
import com.exchangesbot.service.PositionManage;
import com.exchangesbot.until.EventUtil;
import com.exchangesbot.until.KlineCalculateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 十字星策略事件处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CrossEventListener implements EventHandler<Cross> {

    private final PositionManage positionManage;

    @Override
    public void handle(Cross event) {
        List<Kline> klineList = event.getKlineList();
        ConfigParam configParam = event.getConfigParam();
        EventUtil.logCurrentKline(klineList,KlineStrategyEnum.CROSS.getDesc());
        klineProcess(klineList, configParam);
    }

    /**
     * 1.当前k的针尖价格小于等于窗口每根最低价格
     * 2.下影线针是上影线2倍 上影线小于等于下影线的一半（后期配置化） 分为绿柱和红柱 绿柱准确率更高
     * 3.成交量 是前窗口均值的2倍
     *
     * @param klineList
     * @param configParam
     */
    private void klineProcess(List<Kline> klineList, ConfigParam configParam) {
        Integer klineWindowNum = configParam.getKlineWindow();
        if (klineList.size() < klineWindowNum) {
            log.info("当前k线数据小于策略窗口，不予处理");// BLATE-TODO: 2025/1/9  后期ws需要 自动补全窗口
            return;
        }

        List<Kline> window = klineList.subList(klineList.size() - klineWindowNum, klineList.size());

        if (!isLowestPrice(window)) {
            log.info("当前不是最低价格，不符合十字星");
            return;
        }

        Kline currentLine = window.get(window.size() - 1);
        setShadow(currentLine);

        BigDecimal lowerShadow = currentLine.getLowerShadow();
        BigDecimal upperShadow = currentLine.getUpperShadow();
        BigDecimal column = currentLine.getColumn();
        if (!(lowerShadow.compareTo(column.multiply(BigDecimal.valueOf(2))) >= 0)) {
            System.out.println("下影线针不大于等于实体柱2倍，不符合十字星");
            return;
        }
        if (!(lowerShadow.compareTo(upperShadow.multiply(BigDecimal.valueOf(2))) >= 0)) {
            System.out.println("下影线针不大于等于上影线2倍，不符合十字星");
            return;
        }
        BigDecimal quoteVolume = currentLine.getQuoteVolume();
        BigDecimal mean = KlineCalculateUtil.getMean(window);
        if (!(quoteVolume.compareTo(mean.multiply(BigDecimal.valueOf(2))) >= 0)) {
            System.out.println("当前成交量不大于等于周期均值成交量2倍，不符合十字星");
            return;
        }

        BigDecimal closePrice = currentLine.getClosePrice();//收盘价格为买入价格
        positionManage.operation(new Position(KlineStrategyEnum.CROSS, OrderOpEnum.BUY, DateUtil.date(), closePrice,null, configParam, currentLine), UserInfoContext.getUserInfo());
    }


    private static boolean isLowestPrice(List<Kline> kLines) {
        Kline currentLine = kLines.get(kLines.size() - 1);
        BigDecimal lowPrice = currentLine.getLowPrice();
        return kLines.stream().allMatch(kline -> kline.getLowPrice().compareTo(lowPrice) >= 0);
    }

    private static void setShadow(Kline kline) {
        // 1. 计算绿柱还是红柱
        boolean isUp = kline.getClosePrice().compareTo(kline.getOpenPrice()) >= 0;
        kline.setKlineTypeEnum(isUp ? KlineTypeEnum.GREEN : KlineTypeEnum.RED);
        // 2. 计算上下影线 实体柱
        BigDecimal upperShadow;
        BigDecimal lowerShadow;
        BigDecimal column;
        if (isUp) {
            upperShadow = kline.getHighPrice().subtract(kline.getClosePrice());
            lowerShadow = kline.getOpenPrice().subtract(kline.getLowPrice());
            column = kline.getClosePrice().subtract(kline.getOpenPrice());
        } else {
            upperShadow = kline.getHighPrice().subtract(kline.getOpenPrice());
            lowerShadow = kline.getClosePrice().subtract(kline.getLowPrice());
            column = kline.getOpenPrice().subtract(kline.getClosePrice());
        }
        kline.setUpperShadow(upperShadow);
        kline.setLowerShadow(lowerShadow);
        kline.setColumn(column);
    }


    @Override
    public String getSupportEventType() {
        return KlineStrategyEnum.CROSS.getCode();
    }

}