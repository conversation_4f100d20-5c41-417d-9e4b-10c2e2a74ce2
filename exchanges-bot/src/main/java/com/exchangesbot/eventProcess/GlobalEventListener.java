package com.exchangesbot.eventProcess;

import com.exchangesbot.constant.enums.KlineStrategyEnum;
import com.exchangesbot.eventProcess.event.KlineProcessEvent;
import com.exchangesbot.interceptor.UserInfo;
import com.exchangesbot.interceptor.UserInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一事件监听器
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Component
@Slf4j
public class GlobalEventListener {

    @Resource
    private List<EventHandler> handlers;

    private final Map<String, EventHandler> handlerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        handlers.forEach(handler -> handlerMap.put(handler.getSupportEventType(), handler));
    }

//    @Async("defaultThreadPool") 开启异步
    @EventListener
    public void handleEvent(KlineProcessEvent event) {
        KlineStrategyEnum eventType = event.getEventType();
        EventHandler handler = handlerMap.get(eventType.getCode());

        if (!ObjectUtils.isEmpty(handler)) {
            try {
                long start = System.currentTimeMillis();
                log.debug("处理事件开始: {} 事件标志：{}", eventType, event.getTimeStamp());
                UserInfo userInfo = event.getUserInfo();
                UserInfoContext.setUserInfo(userInfo);
                handler.handle(event);
                long end = System.currentTimeMillis();
                log.debug("处理事件结束: {} 事件标志：{} 耗时：{}", eventType, event.getTimeStamp(), end - start);
            } catch (Exception e) {
                log.error("处理事件异常: {}, 事件内容: {} 事件标志：{}", e.getMessage(), event, event.getTimeStamp(), e);
            }
        } else {
            log.warn("未找到事件处理器: {}", eventType);
        }
    }

}