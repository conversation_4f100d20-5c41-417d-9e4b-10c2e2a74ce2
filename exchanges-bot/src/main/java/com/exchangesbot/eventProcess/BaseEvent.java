package com.exchangesbot.eventProcess;

import com.exchangesbot.constant.enums.KlineStrategyEnum;
import lombok.Getter;

/**
 * 扩展新的事件 继承这个即可
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Getter
public abstract class BaseEvent {

    private final long timeStamp;

    protected BaseEvent() {
        this.timeStamp = System.currentTimeMillis();
    }

    /**
     * 维护事件类型
     * @see KlineStrategyEnum
     */
   public abstract KlineStrategyEnum getEventType();

}