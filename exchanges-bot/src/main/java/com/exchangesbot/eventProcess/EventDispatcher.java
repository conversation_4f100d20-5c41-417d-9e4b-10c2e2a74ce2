package com.exchangesbot.eventProcess;

import com.exchangesbot.eventProcess.event.KlineProcessEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 统一事件调度器
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Component
@Slf4j
public class EventDispatcher implements ApplicationEventPublisherAware {

    private ApplicationEventPublisher publisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    public void publish(BaseEvent event) {
        long start = System.currentTimeMillis();
        log.debug("发布事件开始: {} 事件标志：{}", event.getEventType(), event.getTimeStamp());
        publisher.publishEvent(event);
        long end = System.currentTimeMillis();
        log.debug("发布事件结束: {} 事件标志：{} 耗时：{}", event.getEventType(), event.getTimeStamp(), end - start);
    }

    public void publish(List<KlineProcessEvent> eventList) {
        eventList.forEach(this::publish);
    }

}