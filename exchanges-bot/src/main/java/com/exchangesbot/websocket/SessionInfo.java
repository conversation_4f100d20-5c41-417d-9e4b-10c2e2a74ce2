package com.exchangesbot.websocket;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.socket.WebSocketSession;

@Data
@AllArgsConstructor
public class SessionInfo {
    private String ip;
    private WebSocketSession webSocketSession;
    private boolean firstConnected = false;

    public SessionInfo(String ip, WebSocketSession webSocketSession) {
        this.ip = ip;
        this.webSocketSession = webSocketSession;
    }

}
