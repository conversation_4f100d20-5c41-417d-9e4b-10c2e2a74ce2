package com.exchangesbot.websocket;

import com.alibaba.fastjson.JSON;
import com.exchangesbot.constant.enums.InfoStreamEnum;
import com.exchangesbot.resp.WsMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.socket.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Slf4j
public class WsServer implements WebSocketHandler {
    private static final String VERSION = "/v1/";
    //单个信息流 单个ip限制多少链接
    // BLATE-TODO 2025/2/6 目前信息不会乱推送 但是多ip问题需要解决
    private static final int MAX_CONNECTIONS_PER_IP = 1000000000;
    private static final Map<InfoStreamEnum, ConcurrentHashMap<String, LinkedHashSet<SessionInfo>>> onLineUserMap = new ConcurrentHashMap<>();


    @Override
    public void afterConnectionEstablished(@NotNull WebSocketSession session) {
        String wsClientSubStreamType = session.getUri().getPath().replace(VERSION, "");
        String errorMsg = InfoStreamEnum.validateCode(wsClientSubStreamType);
        if (ObjectUtils.isNotEmpty(errorMsg)) {
            sendMessage(errorMsg, session);
            closeSession(session);
            return;
        }
        InfoStreamEnum infoStream = InfoStreamEnum.getEnumByCode(wsClientSubStreamType);
        String clientIp = session.getRemoteAddress().getAddress().getHostAddress();
//        if(clientIp.equals("0:0:0:0:0:0:0:1")){
//            clientIp = "127.0.0.1";
//        }
        subscribe(infoStream, clientIp, session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        String clientIp = session.getRemoteAddress().getAddress().getHostAddress();
        forceUnsubscribe(session);
        log.info("{}断开,当前总订阅数：{}", clientIp, getSubscriptionCountPerStream());
    }

    private void subscribe(InfoStreamEnum infoStream, String ip, WebSocketSession session) {
        // 确保每个 infoStream 类型的Map已经初始化
        Map<String, LinkedHashSet<SessionInfo>> ipSessionMap = onLineUserMap.computeIfAbsent(infoStream, k -> new ConcurrentHashMap<>());
        // 确保每个 ip 对应的 sessionInfos 已经初始化
        LinkedHashSet<SessionInfo> sessionInfos = ipSessionMap.computeIfAbsent(ip, k -> new LinkedHashSet<>());

        if (sessionInfos.size() >= MAX_CONNECTIONS_PER_IP) {
            String msg = "当前达到最大IP限制";
            sendMessage(msg, session);
            sessionInfos.add(new SessionInfo(ip, session));
            closeSession(session);
            return;
        }
        sessionInfos.add(new SessionInfo(ip, session, true));
        log.info("{}建立连接,当前总订阅数：{}", ip, getSubscriptionCountPerStream());
        sendMessage("成功订阅" + infoStream.getDesc(), session);
    }

    private void forceUnsubscribe(WebSocketSession session) {
        onLineUserMap.forEach((k, v) -> {
            String ip = session.getRemoteAddress().getAddress().getHostAddress();
            LinkedHashSet<SessionInfo> sessionInfos = v.get(ip);
            if (!ObjectUtils.isEmpty(sessionInfos)) {
                sessionInfos.removeIf(sessionInfo -> sessionInfo.getWebSocketSession().getId().equals(session.getId()));
            }
            if (sessionInfos.isEmpty()) {
                onLineUserMap.remove(k);
            }
        });
    }

    public static void pushInfoStream(WsMsg wsMsg) {
        InfoStreamEnum stream = wsMsg.getStream();
        ConcurrentHashMap<String, LinkedHashSet<SessionInfo>> webSocketSessions = onLineUserMap.get(stream);
        if (ObjectUtils.isNotEmpty(webSocketSessions)) {
            log.info(webSocketSessions.toString());
            String ip = wsMsg.getIp();
            LinkedHashSet<SessionInfo> sendTargetList;
            if (ObjectUtils.isEmpty(ip)) {
                Collection<LinkedHashSet<SessionInfo>> values = webSocketSessions.values();
                sendTargetList = values.stream().flatMap(Collection::stream).collect(Collectors.toCollection(LinkedHashSet::new));
            } else {
                sendTargetList = webSocketSessions.get(ip);
            }
            if(ObjectUtils.isEmpty(sendTargetList)){
                log.info("无连接不推送");
                return;
            }
            List<WebSocketSession> ipSessionList = sendTargetList.stream().map(SessionInfo::getWebSocketSession).collect(Collectors.toList());
            sendMessage(JSON.toJSONString(wsMsg), ipSessionList);
        }
    }

    private static void sendMessage(String message, WebSocketSession session) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void sendMessage(String message, List<WebSocketSession> sessionList) {
        sessionList.forEach(session -> sendMessage(message, session));
        log.info("已发送：{}",message);
    }

    private void closeSession(WebSocketSession session) {
        try {
            session.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Map<InfoStreamEnum, Integer> getSubscriptionCountPerStream() {
        Map<InfoStreamEnum, Integer> subscriptionCountMap = new HashMap<>();
        for (Map.Entry<InfoStreamEnum, ConcurrentHashMap<String, LinkedHashSet<SessionInfo>>> entry : onLineUserMap.entrySet()) {
            InfoStreamEnum stream = entry.getKey();
            ConcurrentHashMap<String, LinkedHashSet<SessionInfo>> value = entry.getValue();
            int count = value.size();
            subscriptionCountMap.put(stream, count);
            value.forEach((k, v) -> {
                v.forEach(sessionInfo -> {
                    log.info("当前连接详情预览{}  {}", k, v);
                });
            });
        }
        return subscriptionCountMap;
    }


    @Override
    public void handleMessage(@NotNull WebSocketSession session, @NotNull WebSocketMessage<?> message) {
        log.info("处理消息:{}", message);
    }

    @Override
    public void handleTransportError(@NotNull WebSocketSession session, @NotNull Throwable exception) {
        log.info("错误处理");
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}