server:
  port: 9080
spring:
  datasource:
    url: *****************************************************************************************************************
#    url: **********************************************************************************************************************
    username: admin
    password: jianghu666.
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 数据源
    hikari:
      pool-name: DateHikariCP # 连接池名
      minimum-idle: 5 # 最小空闲连接数
      idle-timeout: 180000 # 空闲连接存活最大时间，默认600000（10分钟）
      maximum-pool-size: 10 # 最大连接数，默认10
      auto-commit: true # 从连接池返回的连接的自动提交
      max-lifetime: 180000 # 连接最大存活时间，0表示永久存活，默认1800000（30分钟）
      connection-timeout: 30000 # 连接超时时间，默认30000（30秒）
      connection-test-query: SELECT 1 # 测试连接是否可用的查询语句

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
    map-underscore-to-camel-case: true # 驼峰映射